# AI内容生成应用

一个集成文案生成和图片生成功能的AI应用程序，支持豆包API图片生成。

## 🌟 功能特性

- 🎨 **AI图片生成**: 集成豆包API，支持多种参数配置
- ✍️ **AI文案生成**: 智能文案创作功能
- 🎛️ **参数控制**: 图片尺寸、水印、流式生成等选项
- 🎨 **简洁界面**: 单调色彩设计，专注用户体验
- 🔄 **实时连接**: 自动检测后端服务状态
- 📱 **响应式设计**: 支持移动端和桌面端

## 📁 项目结构

```
AI/
├── backend/                 # Flask后端API
│   ├── app.py              # 主应用文件
│   ├── requirements.txt    # Python依赖
│   ├── config.py          # 配置文件
│   ├── .env               # 环境变量配置
│   └── services/          # 服务模块
│       ├── image_service.py    # 图片生成服务
│       └── text_service.py     # 文案生成服务
├── frontend/              # 前端应用
│   ├── index.html        # 主页面
│   ├── src/              # React源代码（可选）
│   └── package.json      # 依赖配置
├── start.bat             # 一键启动脚本
└── README.md             # 项目说明
```

## 🚀 快速开始

### 方法一：一键启动（推荐）
双击运行 `start.bat` 文件，自动启动前后端服务并打开浏览器。

### 方法二：手动启动

#### 1. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python app.py
```

#### 2. 启动前端服务
```bash
cd frontend
python -m http.server 3000
```

#### 3. 访问应用
打开浏览器访问: http://localhost:3000

## ⚙️ 配置说明

### 环境变量配置
在 `backend/.env` 文件中配置：
```bash
ARK_API_KEY=6070d45c-b204-48dc-8f30-d639129f0b30
SECRET_KEY=your-secret-key
DEBUG=True
HOST=0.0.0.0
PORT=5000
```

### 豆包API参数
支持的图片生成参数：
- **model**: doubao-seedream-4-0-250828
- **size**: 1K, 2K, 4K
- **sequential_image_generation**: enabled/disabled
- **stream**: true/false
- **response_format**: url/b64_json
- **watermark**: true/false

## 🎯 使用指南

### 文案生成
1. 选择"文案生成"标签
2. 输入主题关键词（可选）
3. 选择文案风格：创意/商业/艺术/科技
4. 选择文案长度：简短/中等/详细
5. 点击"生成文案"按钮

### 图片生成
1. 选择"图片生成"标签
2. 输入详细的图片描述
3. 配置生成参数：
   - 图片尺寸（1K/2K/4K）
   - 响应格式（URL/Base64）
   - 水印开关
4. 点击"生成图片"按钮

## 🛠️ 技术栈

### 后端
- **Python Flask**: Web框架
- **豆包API**: 图片生成服务
- **Flask-CORS**: 跨域支持
- **Requests**: HTTP客户端

### 前端
- **HTML5/CSS3/JavaScript**: 原生Web技术
- **响应式设计**: 适配多种设备
- **Fetch API**: 异步请求

## 📊 API接口

### 后端API端点
- `GET /`: 健康检查
- `POST /api/generate-text`: 文案生成
- `POST /api/generate-image`: 图片生成
- `GET /api/options`: 获取配置选项

### 请求示例
```javascript
// 文案生成
fetch('http://localhost:5000/api/generate-text', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    theme: '科技',
    style: '创意',
    length: '中等'
  })
});

// 图片生成
fetch('http://localhost:5000/api/generate-image', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    prompt: '一只可爱的小猫',
    size: '2K',
    watermark: true
  })
});
```
