import requests
import json
import random
from config import Config

class TextService:
    def __init__(self):
        self.api_key = Config.ARK_API_KEY
        self.base_url = Config.ARK_BASE_URL
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }

        # 备用模板（当API调用失败时使用）
        self.fallback_templates = [
            "关于{theme}：这是一个{style}的表达，展现出独特的魅力和深度内涵。",
            "{theme}代表着创新与突破，体现了{style}的精神和价值观念。",
            "在{theme}的世界里，我们发现了{style}的无限可能和广阔前景。",
            "探索{theme}的奥秘，感受{style}带来的震撼和启发。"
        ]
    
    def generate_text(self, theme='', style='创意', length='中等'):
        """
        使用豆包API生成文案

        Args:
            theme (str): 主题关键词
            style (str): 文案风格 (创意/商业/艺术/科技)
            length (str): 文案长度 (简短/中等/详细)

        Returns:
            dict: 生成的文案结果
        """
        try:
            # 首先尝试使用豆包API生成文案
            api_result = self._generate_with_api(theme, style, length)
            if api_result['success']:
                return api_result

            # API失败时使用备用方案
            print(f"API调用失败，使用备用方案: {api_result.get('error', '')}")
            return self._generate_fallback(theme, style, length)

        except Exception as e:
            print(f"文案生成异常: {str(e)}")
            return self._generate_fallback(theme, style, length)

    def _generate_with_api(self, theme, style, length):
        """使用豆包API生成文案"""
        try:
            # 构建提示词
            prompt = self._build_prompt(theme, style, length)

            url = f"{self.base_url}/chat/completions"
            payload = {
                "model": "doubao-pro-4k",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的文案创作专家，擅长根据用户需求创作各种风格的文案内容。请直接输出文案内容，不要包含任何解释或额外说明。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": 200,
                "temperature": 0.8,
                "stream": False
            }

            response = requests.post(url, headers=self.headers, json=payload, timeout=30)
            response.raise_for_status()

            result = response.json()

            if 'choices' in result and len(result['choices']) > 0:
                generated_text = result['choices'][0]['message']['content'].strip()

                return {
                    'success': True,
                    'data': {
                        'text': generated_text,
                        'theme': theme or '通用',
                        'style': style,
                        'length': length,
                        'source': 'API'
                    }
                }
            else:
                return {
                    'success': False,
                    'error': 'API返回格式异常'
                }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'API请求失败: {str(e)}'
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'响应解析失败: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'API调用异常: {str(e)}'
            }
    
    def _build_prompt(self, theme, style, length):
        """构建API请求的提示词"""
        # 风格描述
        style_descriptions = {
            '创意': '富有创意和想象力，语言生动有趣，充满新颖的表达方式',
            '商业': '专业正式，突出价值和优势，具有说服力和吸引力',
            '艺术': '优美典雅，富有诗意和美感，注重意境和情感表达',
            '科技': '现代感强，体现科技前沿，理性客观且具有未来感'
        }

        # 长度要求
        length_requirements = {
            '简短': '50-80字',
            '中等': '100-150字',
            '详细': '200-300字'
        }

        # 构建提示词
        if theme:
            prompt = f"请创作一段关于'{theme}'的{style}风格文案。"
        else:
            prompt = f"请创作一段{style}风格的文案。"

        prompt += f"\n\n要求：\n"
        prompt += f"- 风格：{style_descriptions.get(style, '创意新颖')}\n"
        prompt += f"- 长度：{length_requirements.get(length, '100-150字')}\n"
        prompt += f"- 语言：中文\n"
        prompt += f"- 内容：原创且富有吸引力\n"
        prompt += f"- 格式：直接输出文案内容，不要包含标题或说明"

        return prompt

    def _generate_fallback(self, theme, style, length):
        """备用文案生成方案"""
        try:
            # 使用模板生成备用文案
            template = random.choice(self.fallback_templates)
            base_text = template.format(theme=theme or '创新理念', style=style)

            # 根据长度调整
            if length == '简短':
                final_text = base_text[:50] + '。'
            elif length == '详细':
                additions = [
                    '这不仅是一种表达方式，更是一种生活态度和价值追求。',
                    '通过深入探索和实践，我们能够发现更多的可能性和机遇。',
                    '让我们一起拥抱变化，创造属于我们的精彩未来。'
                ]
                final_text = base_text + random.choice(additions)
            else:
                final_text = base_text

            return {
                'success': True,
                'data': {
                    'text': final_text,
                    'theme': theme or '通用',
                    'style': style,
                    'length': length,
                    'source': '备用模板'
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'备用方案失败: {str(e)}'
            }
    
    def get_available_styles(self):
        """获取可用的文案风格"""
        return ['创意', '商业', '艺术', '科技']
    
    def get_available_lengths(self):
        """获取可用的文案长度"""
        return ['简短', '中等', '详细']
