<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI内容生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f8f9fa;
            color: #2c3e50;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 16px;
        }

        .status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: #f8f9fa;
            border-radius: 20px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }

        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 32px;
            border-bottom: 1px solid #e9ecef;
        }

        .tab {
            padding: 12px 24px;
            border: none;
            background: #e9ecef;
            color: #495057;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            margin-right: 8px;
            transition: all 0.2s ease;
        }

        .tab.active {
            background: #6c757d;
            color: white;
            border-bottom: 2px solid #6c757d;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .card-header {
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e9ecef;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
            transition: border-color 0.2s ease;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #6c757d;
            box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        .options-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #6c757d;
            color: white;
        }

        .btn-primary:hover {
            background-color: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e9ecef;
            border-top: 2px solid #6c757d;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 6px;
            border: 1px solid #f5c6cb;
            margin-bottom: 16px;
        }

        .result {
            margin-top: 24px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .generated-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>AI内容生成器</h1>
            <p>智能文案创作 & 图片生成平台</p>
            <div class="status">
                <div class="status-dot status-disconnected" id="statusDot"></div>
                <span id="statusText">检查连接中...</span>
            </div>
        </header>

        <!-- 功能切换标签 -->
        <div class="tabs">
            <button class="tab active" onclick="switchTab('text')">✍️ 文案生成</button>
            <button class="tab" onclick="switchTab('image')">🎨 图片生成</button>
        </div>

        <!-- 连接失败提示 -->
        <div id="connectionError" class="error hidden">
            <strong>后端服务连接失败</strong><br>
            请确保后端服务已启动并运行在 http://localhost:5000<br>
            启动命令: <code>cd backend && python app.py</code>
        </div>

        <!-- 文案生成模块 -->
        <div id="textGenerator" class="card">
            <div class="card-header">
                <h2 class="card-title">✍️ AI文案生成</h2>
            </div>
            
            <div id="textError" class="error hidden"></div>
            
            <div class="form-group">
                <label class="form-label">主题关键词</label>
                <input type="text" id="textTheme" class="form-input" placeholder="输入主题关键词（可选）">
            </div>
            
            <div class="options-grid">
                <div class="form-group">
                    <label class="form-label">文案风格</label>
                    <select id="textStyle" class="form-select">
                        <option value="创意">创意</option>
                        <option value="商业">商业</option>
                        <option value="艺术">艺术</option>
                        <option value="科技">科技</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">文案长度</label>
                    <select id="textLength" class="form-select">
                        <option value="简短">简短</option>
                        <option value="中等" selected>中等</option>
                        <option value="详细">详细</option>
                    </select>
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="generateText()" id="textBtn">
                生成文案
            </button>
            
            <div id="textResult" class="result hidden">
                <h3>生成结果</h3>
                <div id="textContent"></div>
            </div>
        </div>

        <!-- 图片生成模块 -->
        <div id="imageGenerator" class="card hidden">
            <div class="card-header">
                <h2 class="card-title">🎨 AI图片生成</h2>
            </div>
            
            <div id="imageError" class="error hidden"></div>
            
            <div class="form-group">
                <label class="form-label">图片描述 *</label>
                <textarea id="imagePrompt" class="form-textarea" placeholder="请详细描述您想要生成的图片内容..." rows="4"></textarea>
            </div>
            
            <div class="options-grid">
                <div class="form-group">
                    <label class="form-label">图片尺寸</label>
                    <select id="imageSize" class="form-select">
                        <option value="1K">1K</option>
                        <option value="2K" selected>2K</option>
                        <option value="4K">4K</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">响应格式</label>
                    <select id="imageFormat" class="form-select">
                        <option value="url" selected>URL链接</option>
                        <option value="b64_json">Base64编码</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">水印</label>
                    <select id="imageWatermark" class="form-select">
                        <option value="true" selected>启用</option>
                        <option value="false">禁用</option>
                    </select>
                </div>
            </div>
            
            <button class="btn btn-primary" onclick="generateImage()" id="imageBtn">
                生成图片
            </button>
            
            <div id="imageResult" class="result hidden">
                <h3>生成结果</h3>
                <div id="imageContent"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let connectionStatus = 'checking';
        
        // 检查后端连接
        async function checkConnection() {
            try {
                const response = await fetch('http://localhost:5000/');
                if (response.ok) {
                    connectionStatus = 'connected';
                    document.getElementById('statusDot').className = 'status-dot status-connected';
                    document.getElementById('statusText').textContent = '后端服务: 已连接';
                    document.getElementById('connectionError').classList.add('hidden');
                } else {
                    throw new Error('连接失败');
                }
            } catch (error) {
                connectionStatus = 'disconnected';
                document.getElementById('statusDot').className = 'status-dot status-disconnected';
                document.getElementById('statusText').textContent = '后端服务: 连接失败';
                document.getElementById('connectionError').classList.remove('hidden');
            }
        }
        
        // 切换标签
        function switchTab(tab) {
            // 更新标签样式
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示/隐藏内容
            if (tab === 'text') {
                document.getElementById('textGenerator').classList.remove('hidden');
                document.getElementById('imageGenerator').classList.add('hidden');
            } else {
                document.getElementById('textGenerator').classList.add('hidden');
                document.getElementById('imageGenerator').classList.remove('hidden');
            }
        }
        
        // 生成文案
        async function generateText() {
            const btn = document.getElementById('textBtn');
            const errorDiv = document.getElementById('textError');
            const resultDiv = document.getElementById('textResult');
            
            btn.disabled = true;
            btn.innerHTML = '<div class="spinner"></div>生成中...';
            errorDiv.classList.add('hidden');
            resultDiv.classList.add('hidden');
            
            try {
                const response = await fetch('http://localhost:5000/api/generate-text', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        theme: document.getElementById('textTheme').value,
                        style: document.getElementById('textStyle').value,
                        length: document.getElementById('textLength').value
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('textContent').innerHTML = `
                        <p style="font-size: 16px; line-height: 1.8; margin-bottom: 16px;">${data.data.text}</p>
                        <div style="font-size: 14px; color: #6c757d;">
                            <strong>主题:</strong> ${data.data.theme || '随机'} | 
                            <strong>风格:</strong> ${data.data.style} | 
                            <strong>长度:</strong> ${data.data.length}
                        </div>
                    `;
                    resultDiv.classList.remove('hidden');
                } else {
                    errorDiv.textContent = data.error || '文案生成失败';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = '网络错误: ' + error.message;
                errorDiv.classList.remove('hidden');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '生成文案';
            }
        }
        
        // 生成图片
        async function generateImage() {
            const btn = document.getElementById('imageBtn');
            const errorDiv = document.getElementById('imageError');
            const resultDiv = document.getElementById('imageResult');
            const prompt = document.getElementById('imagePrompt').value.trim();
            
            if (!prompt) {
                errorDiv.textContent = '请输入图片描述';
                errorDiv.classList.remove('hidden');
                return;
            }
            
            btn.disabled = true;
            btn.innerHTML = '<div class="spinner"></div>生成中...';
            errorDiv.classList.add('hidden');
            resultDiv.classList.add('hidden');
            
            try {
                const response = await fetch('http://localhost:5000/api/generate-image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        prompt: prompt,
                        size: document.getElementById('imageSize').value,
                        response_format: document.getElementById('imageFormat').value,
                        watermark: document.getElementById('imageWatermark').value === 'true',
                        sequential_image_generation: 'disabled',
                        stream: false
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.data.data && data.data.data.length > 0) {
                    const imageData = data.data.data[0];
                    document.getElementById('imageContent').innerHTML = `
                        <img src="${imageData.url}" alt="生成的图片" class="generated-image" 
                             onerror="this.style.display='none'; document.getElementById('imageError').textContent='图片加载失败'; document.getElementById('imageError').classList.remove('hidden');">
                        <p style="margin-top: 12px; color: #6c757d; font-size: 14px;">
                            尺寸: ${imageData.size}
                        </p>
                    `;
                    resultDiv.classList.remove('hidden');
                } else {
                    errorDiv.textContent = data.error || '图片生成失败';
                    errorDiv.classList.remove('hidden');
                }
            } catch (error) {
                errorDiv.textContent = '网络错误: ' + error.message;
                errorDiv.classList.remove('hidden');
            } finally {
                btn.disabled = false;
                btn.innerHTML = '生成图片';
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            // 每30秒检查一次连接
            setInterval(checkConnection, 30000);
        });
    </script>
</body>
</html>
