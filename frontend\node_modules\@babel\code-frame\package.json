{"_from": "@babel/code-frame@^7.27.1", "_id": "@babel/code-frame@7.27.1", "_inBundle": false, "_integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "_location": "/@babel/code-frame", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/code-frame@^7.27.1", "name": "@babel/code-frame", "escapedName": "@babel%2fcode-frame", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/core", "/@babel/template", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "_shasum": "200f715e66d52a23b221a9435534a91cc13ad5be", "_spec": "@babel/code-frame@^7.27.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "deprecated": false, "description": "Generate errors that contain a code frame that point to source locations.", "devDependencies": {"import-meta-resolve": "^4.1.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-code-frame", "license": "MIT", "main": "./lib/index.js", "name": "@babel/code-frame", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-code-frame"}, "type": "commonjs", "version": "7.27.1"}