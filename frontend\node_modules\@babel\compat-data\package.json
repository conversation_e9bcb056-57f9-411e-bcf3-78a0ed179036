{"_from": "@babel/compat-data@^7.27.2", "_id": "@babel/compat-data@7.28.4", "_inBundle": false, "_integrity": "sha512-YsmSKC29MJwf0gF8Rjjrg5LQCmyh+j/nD8/eP7f+BeoQTKYqs9RoWbjGOdy0+1Ekr68RJZMUOPVQaQisnIo4Rw==", "_location": "/@babel/compat-data", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/compat-data@^7.27.2", "name": "@babel/compat-data", "escapedName": "@babel%2fcompat-data", "scope": "@babel", "rawSpec": "^7.27.2", "saveSpec": null, "fetchSpec": "^7.27.2"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.4.tgz", "_shasum": "96fdf1af1b8859c8474ab39c295312bfb7c24b04", "_spec": "@babel/compat-data@^7.27.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "The compat-data to determine required Babel plugins", "devDependencies": {"@mdn/browser-compat-data": "^6.0.8", "core-js-compat": "^3.43.0", "electron-to-chromium": "^1.5.140"}, "engines": {"node": ">=6.9.0"}, "exports": {"./plugins": "./plugins.js", "./native-modules": "./native-modules.js", "./corejs2-built-ins": "./corejs2-built-ins.js", "./corejs3-shipped-proposals": "./corejs3-shipped-proposals.js", "./overlapping-plugins": "./overlapping-plugins.js", "./plugin-bugfixes": "./plugin-bugfixes.js"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "compat-table", "compat-data"], "license": "MIT", "name": "@babel/compat-data", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-compat-data"}, "scripts": {"build-data": "./scripts/download-compat-table.sh && node ./scripts/build-data.mjs && node ./scripts/build-modules-support.mjs && node ./scripts/build-bugfixes-targets.mjs"}, "type": "commonjs", "version": "7.28.4"}