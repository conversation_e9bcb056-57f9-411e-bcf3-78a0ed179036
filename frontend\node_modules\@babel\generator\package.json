{"_from": "@babel/generator@^7.28.3", "_id": "@babel/generator@7.28.3", "_inBundle": false, "_integrity": "sha512-3lSpxGgvnmZznmBkCRnVREPUFJv2wrv9iAoFDvADJc0ypmdOxdUtcLeBgBJ6zE0PMeTKnxeQzyk0xTBq4Ep7zw==", "_location": "/@babel/generator", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/generator@^7.28.3", "name": "@babel/generator", "escapedName": "@babel%2fgenerator", "scope": "@babel", "rawSpec": "^7.28.3", "saveSpec": null, "fetchSpec": "^7.28.3"}, "_requiredBy": ["/@babel/core", "/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.28.3.tgz", "_shasum": "9626c1741c650cbac39121694a0f2d7451b8ef3e", "_spec": "@babel/generator@^7.28.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.28.3", "@babel/types": "^7.28.2", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "deprecated": false, "description": "Turns an AST into code.", "devDependencies": {"@babel/core": "^7.28.3", "@babel/helper-fixtures": "^7.28.0", "@babel/plugin-transform-typescript": "^7.28.0", "@jridgewell/sourcemap-codec": "^1.5.3", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "files": ["lib"], "homepage": "https://babel.dev/docs/en/next/babel-generator", "license": "MIT", "main": "./lib/index.js", "name": "@babel/generator", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "type": "commonjs", "version": "7.28.3"}