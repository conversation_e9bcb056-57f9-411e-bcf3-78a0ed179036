{"_from": "@babel/helper-globals@^7.28.0", "_id": "@babel/helper-globals@7.28.0", "_inBundle": false, "_integrity": "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==", "_location": "/@babel/helper-globals", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-globals@^7.28.0", "name": "@babel/helper-globals", "escapedName": "@babel%2fhelper-globals", "scope": "@babel", "rawSpec": "^7.28.0", "saveSpec": null, "fetchSpec": "^7.28.0"}, "_requiredBy": ["/@babel/traverse"], "_resolved": "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz", "_shasum": "b9430df2aa4e17bc28665eadeae8aa1d985e6674", "_spec": "@babel/helper-globals@^7.28.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\traverse", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A collection of JavaScript globals for Babel internal usage", "devDependencies": {"globals": "^16.1.0"}, "engines": {"node": ">=6.9.0"}, "exports": {"./data/browser-upper.json": "./data/browser-upper.json", "./data/builtin-lower.json": "./data/builtin-lower.json", "./data/builtin-upper.json": "./data/builtin-upper.json", "./package.json": "./package.json"}, "homepage": "https://github.com/babel/babel#readme", "keywords": ["babel", "globals"], "license": "MIT", "name": "@babel/helper-globals", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-globals"}, "type": "commonjs", "version": "7.28.0"}