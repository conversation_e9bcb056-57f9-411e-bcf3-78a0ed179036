{"_from": "@babel/helper-module-imports@^7.27.1", "_id": "@babel/helper-module-imports@7.27.1", "_inBundle": false, "_integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "_location": "/@babel/helper-module-imports", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-module-imports@^7.27.1", "name": "@babel/helper-module-imports", "escapedName": "@babel%2fhelper-module-imports", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@babel/helper-module-transforms"], "_resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "_shasum": "7ef769a323e2655e126673bb6d2d6913bbead204", "_spec": "@babel/helper-module-imports@^7.27.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\helper-module-transforms", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "deprecated": false, "description": "Babel helper functions for inserting module loads", "devDependencies": {"@babel/core": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-module-imports", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-module-imports", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-imports"}, "type": "commonjs", "version": "7.27.1"}