{"_from": "@babel/helper-module-transforms@^7.28.3", "_id": "@babel/helper-module-transforms@7.28.3", "_inBundle": false, "_integrity": "sha512-gytXUbs8k2sXS9PnQptz5o0QnpLL51SwASIORY6XaBKF88nsOT0Zw9szLqlSGQDP/4TljBAD5y98p2U1fqkdsw==", "_location": "/@babel/helper-module-transforms", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helper-module-transforms@^7.28.3", "name": "@babel/helper-module-transforms", "escapedName": "@babel%2fhelper-module-transforms", "scope": "@babel", "rawSpec": "^7.28.3", "saveSpec": null, "fetchSpec": "^7.28.3"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.28.3.tgz", "_shasum": "a2b37d3da3b2344fe085dab234426f2b9a2fa5f6", "_spec": "@babel/helper-module-transforms@^7.28.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.28.3"}, "deprecated": false, "description": "Babel helper functions for implementing ES6 module transformations", "devDependencies": {"@babel/core": "^7.28.3"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-module-transforms", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helper-module-transforms", "peerDependencies": {"@babel/core": "^7.0.0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helper-module-transforms"}, "type": "commonjs", "version": "7.28.3"}