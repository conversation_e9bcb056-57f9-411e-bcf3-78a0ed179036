{"_from": "@babel/helpers@^7.28.4", "_id": "@babel/helpers@7.28.4", "_inBundle": false, "_integrity": "sha512-HFN59MmQXGHVyYadKLVumYsA9dBFun/ldYxipEjzA4196jpLZd8UjEEBLkbEkvfYreDqJhZxYAWFPtrfhNpj4w==", "_location": "/@babel/helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/helpers@^7.28.4", "name": "@babel/helpers", "escapedName": "@babel%2fhelpers", "scope": "@babel", "rawSpec": "^7.28.4", "saveSpec": null, "fetchSpec": "^7.28.4"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.4.tgz", "_shasum": "fe07274742e95bdf7cf1443593eeb8926ab63827", "_spec": "@babel/helpers@^7.28.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.4"}, "deprecated": false, "description": "Collection of helper functions used by Babel transforms.", "devDependencies": {"@babel/generator": "^7.28.3", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.28.4", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "main": "./lib/index.js", "name": "@babel/helpers", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "type": "commonjs", "version": "7.28.4"}