{"_from": "@babel/plugin-transform-react-jsx-source@^7.27.1", "_id": "@babel/plugin-transform-react-jsx-source@7.27.1", "_inBundle": false, "_integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "_location": "/@babel/plugin-transform-react-jsx-source", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/plugin-transform-react-jsx-source@^7.27.1", "name": "@babel/plugin-transform-react-jsx-source", "escapedName": "@babel%2fplugin-transform-react-jsx-source", "scope": "@babel", "rawSpec": "^7.27.1", "saveSpec": null, "fetchSpec": "^7.27.1"}, "_requiredBy": ["/@vitejs/plugin-react"], "_resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "_shasum": "dcfe2c24094bb757bf73960374e7c55e434f19f0", "_spec": "@babel/plugin-transform-react-jsx-source@^7.27.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@vitejs\\plugin-react", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "bundleDependencies": false, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "deprecated": false, "description": "Add a __source prop to all JSX Elements", "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-react-jsx-source", "keywords": ["babel-plugin"], "license": "MIT", "main": "./lib/index.js", "name": "@babel/plugin-transform-react-jsx-source", "peerDependencies": {"@babel/core": "^7.0.0-0"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-react-jsx-source"}, "type": "commonjs", "version": "7.27.1"}