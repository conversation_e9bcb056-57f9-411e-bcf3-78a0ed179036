{"_from": "@babel/traverse@^7.28.4", "_id": "@babel/traverse@7.28.4", "_inBundle": false, "_integrity": "sha512-YEzuboP2qvQavAcjgQNVgsvHIDv6ZpwXvcvjmyySP2DIMuByS/6ioU5G9pYrWHM6T2YDfc7xga9iNzYOs12CFQ==", "_location": "/@babel/traverse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@babel/traverse@^7.28.4", "name": "@babel/traverse", "escapedName": "@babel%2ftraverse", "scope": "@babel", "rawSpec": "^7.28.4", "saveSpec": null, "fetchSpec": "^7.28.4"}, "_requiredBy": ["/@babel/core", "/@babel/helper-module-imports", "/@babel/helper-module-transforms"], "_resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.4.tgz", "_shasum": "8d456101b96ab175d487249f60680221692b958b", "_spec": "@babel/traverse@^7.28.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "The Babel Team", "url": "https://babel.dev/team"}, "bugs": {"url": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20traverse%22+is%3Aopen"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.3", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.4", "@babel/template": "^7.27.2", "@babel/types": "^7.28.4", "debug": "^4.3.1"}, "deprecated": false, "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "devDependencies": {"@babel/core": "^7.28.4", "@babel/helper-plugin-test-runner": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://babel.dev/docs/en/next/babel-traverse", "license": "MIT", "main": "./lib/index.js", "name": "@babel/traverse", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/babel/babel.git", "directory": "packages/babel-traverse"}, "type": "commonjs", "version": "7.28.4"}