{"_from": "@esbuild/win32-x64@0.21.5", "_id": "@esbuild/win32-x64@0.21.5", "_inBundle": false, "_integrity": "sha512-tQd/1efJuzPC6rCFwEvLtci/xNFcTZknmXs98FYDfGE4wP9ClFV98nyKrzJKVPMhdDnjzLhdUyMX4PsQAPjwIw==", "_location": "/@esbuild/win32-x64", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@esbuild/win32-x64@0.21.5", "name": "@esbuild/win32-x64", "escapedName": "@esbuild%2fwin32-x64", "scope": "@esbuild", "rawSpec": "0.21.5", "saveSpec": null, "fetchSpec": "0.21.5"}, "_requiredBy": ["/esbuild"], "_resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz", "_shasum": "acad351d582d157bb145535db2a6ff53dd514b5c", "_spec": "@esbuild/win32-x64@0.21.5", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\esbuild", "bugs": {"url": "https://github.com/evanw/esbuild/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "The Windows 64-bit binary for esbuild, a JavaScript bundler.", "engines": {"node": ">=12"}, "homepage": "https://github.com/evanw/esbuild#readme", "license": "MIT", "name": "@esbuild/win32-x64", "os": ["win32"], "preferUnplugged": true, "repository": {"type": "git", "url": "git+https://github.com/evanw/esbuild.git"}, "version": "0.21.5"}