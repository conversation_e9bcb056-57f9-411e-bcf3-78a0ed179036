{"_from": "@eslint-community/eslint-utils@^4.2.0", "_id": "@eslint-community/eslint-utils@4.9.0", "_inBundle": false, "_integrity": "sha512-ayVFHdtZ+hsq1t2Dy24wCmGXGe4q9Gu3smhLYALJrr473ZH27MsnSL+LKUlimp4BWJqMDMLmPpx/Q9R3OAlL4g==", "_location": "/@eslint-community/eslint-utils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@eslint-community/eslint-utils@^4.2.0", "name": "@eslint-community/eslint-utils", "escapedName": "@eslint-community%2feslint-utils", "scope": "@eslint-community", "rawSpec": "^4.2.0", "saveSpec": null, "fetchSpec": "^4.2.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.9.0.tgz", "_shasum": "7308df158e064f0dd8b8fdb58aa14fa2a7f913b3", "_spec": "@eslint-community/eslint-utils@^4.2.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/eslint-community/eslint-utils/issues"}, "bundleDependencies": false, "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "deprecated": false, "description": "Utilities for ESLint plugins.", "devDependencies": {"@eslint-community/eslint-plugin-mysticatea": "^15.6.1", "@types/eslint": "^9.6.1", "@types/estree": "^1.0.7", "@typescript-eslint/parser": "^5.62.0", "@typescript-eslint/types": "^5.62.0", "c8": "^8.0.1", "dot-prop": "^7.2.0", "eslint": "^8.57.1", "installed-check": "^8.0.1", "knip": "^5.33.3", "mocha": "^9.2.2", "npm-run-all2": "^6.2.3", "opener": "^1.5.2", "prettier": "2.8.8", "rimraf": "^3.0.2", "rollup": "^2.79.2", "rollup-plugin-dts": "^4.2.3", "rollup-plugin-sourcemaps": "^0.6.3", "semver": "^7.6.3", "typescript": "^4.9.5", "vitepress": "^1.4.1", "warun": "^1.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "files": ["index.*"], "funding": "https://opencollective.com/eslint", "homepage": "https://github.com/eslint-community/eslint-utils#readme", "keywords": ["eslint"], "license": "MIT", "main": "index", "module": "index.mjs", "name": "@eslint-community/eslint-utils", "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/eslint-community/eslint-utils.git"}, "scripts": {"build": "npm run build:dts && npm run build:rollup", "build:dts": "tsc -p tsconfig.build.json", "build:rollup": "rollup -c", "clean": "rimraf .nyc_output coverage index.* dist", "coverage": "opener ./coverage/lcov-report/index.html", "docs:build": "vitepress build docs", "docs:watch": "vitepress dev docs", "format": "npm run -s format:prettier -- --write", "format:check": "npm run -s format:prettier -- --check", "format:prettier": "prettier .", "lint": "run-p lint:*", "lint:eslint": "eslint .", "lint:format": "npm run -s format:check", "lint:installed-check": "installed-check -v -i installed-check -i npm-run-all2 -i knip -i rollup-plugin-dts", "lint:knip": "knip", "postversion": "git push && git push --tags", "prebuild": "npm run -s clean", "preversion": "npm run test-coverage && npm run -s build", "prewatch": "npm run -s clean", "test": "mocha --reporter dot \"test/*.mjs\"", "test-coverage": "c8 mocha --reporter dot \"test/*.mjs\"", "watch": "warun \"{src,test}/**/*.mjs\" -- npm run -s test:mocha"}, "sideEffects": false, "version": "4.9.0"}