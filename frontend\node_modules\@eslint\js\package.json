{"_from": "@eslint/js@8.57.1", "_id": "@eslint/js@8.57.1", "_inBundle": false, "_integrity": "sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==", "_location": "/@eslint/js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@eslint/js@8.57.1", "name": "@eslint/js", "escapedName": "@eslint%2fjs", "scope": "@eslint", "rawSpec": "8.57.1", "saveSpec": null, "fetchSpec": "8.57.1"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz", "_shasum": "de633db3ec2ef6a3c89e2f19038063e8a122e2c2", "_spec": "@eslint/js@8.57.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "bugs": {"url": "https://github.com/eslint/eslint/issues/"}, "bundleDependencies": false, "deprecated": false, "description": "ESLint JavaScript language implementation", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["LICENSE", "README.md", "src"], "homepage": "https://eslint.org", "keywords": ["javascript", "eslint-plugin", "eslint"], "license": "MIT", "main": "./src/index.js", "name": "@eslint/js", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint.git", "directory": "packages/js"}, "scripts": {}, "version": "8.57.1"}