{"_from": "@humanwhocodes/module-importer@^1.0.1", "_id": "@humanwhocodes/module-importer@1.0.1", "_inBundle": false, "_integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "_location": "/@humanwhocodes/module-importer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@humanwhocodes/module-importer@^1.0.1", "name": "@humanwhocodes/module-importer", "escapedName": "@humanwhocodes%2fmodule-importer", "scope": "@humanwhocodes", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "_shasum": "af5b2691a22b44be847b0ca81641c5fb6ad0172c", "_spec": "@humanwhocodes/module-importer@^1.0.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/humanwhocodes/module-importer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Universal module importer for Node.js", "devDependencies": {"@types/node": "^18.7.6", "c8": "7.12.0", "chai": "4.3.6", "eslint": "8.22.0", "lint-staged": "13.0.3", "mocha": "9.2.2", "rollup": "2.78.0", "typescript": "4.7.4", "yorkie": "2.0.0"}, "engines": {"node": ">=12.22"}, "exports": {"require": "./src/module-importer.cjs", "import": "./src/module-importer.js"}, "files": ["dist", "src"], "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/humanwhocodes/module-importer#readme", "keywords": ["modules", "esm", "commonjs"], "license": "Apache-2.0", "lint-staged": {"*.js": ["eslint --fix"]}, "main": "src/module-importer.cjs", "module": "src/module-importer.js", "name": "@humanwhocodes/module-importer", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/humanwhocodes/module-importer.git"}, "scripts": {"build": "rollup -c && tsc", "lint": "eslint src/ tests/", "prepare": "npm run build", "test": "npm run test:unit && npm run test:build", "test:build": "node tests/pkg.test.cjs && node tests/pkg.test.mjs", "test:unit": "c8 mocha tests/module-importer.test.js"}, "type": "module", "types": "dist/module-importer.d.ts", "version": "1.0.1"}