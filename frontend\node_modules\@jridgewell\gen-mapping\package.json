{"_from": "@jridgewell/gen-mapping@^0.3.12", "_id": "@jridgewell/gen-mapping@0.3.13", "_inBundle": false, "_integrity": "sha512-2kkt/7niJ6MgEPxF0bYdQ6etZaA+fQvDcLKckhy1yIQOzaoKjBBjSj63/aLVjYE3qhRt5dvM+uUyfCg6UKCBbA==", "_location": "/@jridgewell/gen-mapping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/gen-mapping@^0.3.12", "name": "@jridgewell/gen-mapping", "escapedName": "@jridgewell%2fgen-mapping", "scope": "@jridgewell", "rawSpec": "^0.3.12", "saveSpec": null, "fetchSpec": "^0.3.12"}, "_requiredBy": ["/@babel/generator", "/@j<PERSON><PERSON>/remapping"], "_resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.13.tgz", "_shasum": "6342a19f44347518c93e43b1ac69deb3c4656a1f", "_spec": "@jridgewell/gen-mapping@^0.3.12", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\generator", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}, "deprecated": false, "description": "Generate source maps", "exports": {".": [{"import": {"types": "./types/gen-mapping.d.mts", "default": "./dist/gen-mapping.mjs"}, "default": {"types": "./types/gen-mapping.d.cts", "default": "./dist/gen-mapping.umd.js"}}, "./dist/gen-mapping.umd.js"], "./package.json": "./package.json"}, "files": ["dist", "src", "types"], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/gen-mapping", "keywords": ["source", "map"], "license": "MIT", "main": "dist/gen-mapping.umd.js", "module": "dist/gen-mapping.mjs", "name": "@jridgewell/gen-mapping", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/gen-mapping"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs gen-mapping.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'"}, "types": "types/gen-mapping.d.cts", "version": "0.3.13"}