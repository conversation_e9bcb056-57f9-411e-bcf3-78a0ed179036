{"_from": "@jridgewell/remapping@^2.3.5", "_id": "@jridgewell/remapping@2.3.5", "_inBundle": false, "_integrity": "sha512-LI9u/+laYG4Ds1TDKSJW2YPrIlcVYOwi2fUC6xB43lueCjgxV4lffOCZCtYFiH6TNOX+tQKXx97T4IKHbhyHEQ==", "_location": "/@j<PERSON><PERSON>/remapping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/remapping@^2.3.5", "name": "@jridgewell/remapping", "escapedName": "@jridgewell%2fremapping", "scope": "@jridgewell", "rawSpec": "^2.3.5", "saveSpec": null, "fetchSpec": "^2.3.5"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/@jridgewell/remapping/-/remapping-2.3.5.tgz", "_shasum": "375c476d1972947851ba1e15ae8f123047445aa1", "_spec": "@jridgewell/remapping@^2.3.5", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "deprecated": false, "description": "Remap sequential sourcemaps through transformations to point at the original source code", "devDependencies": {"source-map": "0.6.1"}, "exports": {".": [{"import": {"types": "./types/remapping.d.mts", "default": "./dist/remapping.mjs"}, "default": {"types": "./types/remapping.d.cts", "default": "./dist/remapping.umd.js"}}, "./dist/remapping.umd.js"], "./package.json": "./package.json"}, "files": ["dist", "src", "types"], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/remapping", "keywords": ["source", "map", "remap"], "license": "MIT", "main": "dist/remapping.umd.js", "module": "dist/remapping.mjs", "name": "@jridgewell/remapping", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/remapping"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.js", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs remapping.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'"}, "types": "types/remapping.d.cts", "version": "2.3.5"}