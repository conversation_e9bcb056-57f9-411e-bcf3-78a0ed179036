{"_from": "@jridgewell/resolve-uri@^3.1.0", "_id": "@jridgewell/resolve-uri@3.1.2", "_inBundle": false, "_integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "_location": "/@j<PERSON><PERSON>/resolve-uri", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/resolve-uri@^3.1.0", "name": "@jridgewell/resolve-uri", "escapedName": "@jridgewell%2fresolve-uri", "scope": "@jridgewell", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/@jridgewell/trace-mapping"], "_resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "_shasum": "7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6", "_spec": "@jridgewell/resolve-uri@^3.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@jridgewell\\trace-mapping", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/resolve-uri/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Resolve a URI relative to an optional base URI", "devDependencies": {"@jridgewell/resolve-uri-latest": "npm:@j<PERSON><PERSON>/resolve-uri@*", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "engines": {"node": ">=6.0.0"}, "exports": {".": [{"types": "./dist/types/resolve-uri.d.ts", "browser": "./dist/resolve-uri.umd.js", "require": "./dist/resolve-uri.umd.js", "import": "./dist/resolve-uri.mjs"}, "./dist/resolve-uri.umd.js"], "./package.json": "./package.json"}, "files": ["dist"], "homepage": "https://github.com/jridgewell/resolve-uri#readme", "keywords": ["resolve", "uri", "url", "path"], "license": "MIT", "main": "dist/resolve-uri.umd.js", "module": "dist/resolve-uri.mjs", "name": "@jridgewell/resolve-uri", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/resolve-uri.git"}, "scripts": {"build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.js", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "pretest": "run-s build:rollup", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:coverage": "c8 mocha", "test:debug": "mocha --inspect-brk", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "mocha", "test:watch": "mocha --watch"}, "types": "dist/types/resolve-uri.d.ts", "version": "3.1.2"}