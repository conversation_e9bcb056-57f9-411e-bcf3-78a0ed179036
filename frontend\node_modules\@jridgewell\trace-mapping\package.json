{"_from": "@jridgewell/trace-mapping@^0.3.28", "_id": "@jridgewell/trace-mapping@0.3.31", "_inBundle": false, "_integrity": "sha512-zzNR+SdQSDJzc8joaeP8QQoCQr8NuYx2dIIytl1QeBEZHJ9uW6hebsrYgbz8hJwUQao3TWCMtmfV8Nu1twOLAw==", "_location": "/@jridgewell/trace-mapping", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@jridgewell/trace-mapping@^0.3.28", "name": "@jridgewell/trace-mapping", "escapedName": "@jridgewell%2ftrace-mapping", "scope": "@jridgewell", "rawSpec": "^0.3.28", "saveSpec": null, "fetchSpec": "^0.3.28"}, "_requiredBy": ["/@babel/generator", "/@jridgewell/gen-mapping", "/@j<PERSON><PERSON>/remapping"], "_resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.31.tgz", "_shasum": "db15d6781c931f3a251a3dac39501c98a6082fd0", "_spec": "@jridgewell/trace-mapping@^0.3.28", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\generator", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jridgewell/sourcemaps/issues"}, "bundleDependencies": false, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}, "deprecated": false, "description": "Trace the original position through a source map", "exports": {".": [{"import": {"types": "./types/trace-mapping.d.mts", "default": "./dist/trace-mapping.mjs"}, "default": {"types": "./types/trace-mapping.d.cts", "default": "./dist/trace-mapping.umd.js"}}, "./dist/trace-mapping.umd.js"], "./package.json": "./package.json"}, "files": ["dist", "src", "types"], "homepage": "https://github.com/jridgewell/sourcemaps/tree/main/packages/trace-mapping", "keywords": ["source", "map"], "license": "MIT", "main": "dist/trace-mapping.umd.js", "module": "dist/trace-mapping.mjs", "name": "@jridgewell/trace-mapping", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/sourcemaps.git", "directory": "packages/trace-mapping"}, "scripts": {"benchmark": "run-s build:code benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.mjs", "build": "run-s -n build:code build:types", "build:code": "node ../../esbuild.mjs trace-mapping.ts", "build:types": "run-s build:types:force build:types:emit build:types:mts", "build:types:emit": "tsc --project tsconfig.build.json", "build:types:force": "rimraf tsconfig.build.tsbuildinfo", "build:types:mts": "node ../../mts-types.mjs", "clean": "run-s -n clean:code clean:types", "clean:code": "tsc --build --clean tsconfig.build.json", "clean:types": "rimraf dist types", "lint": "run-s -n lint:types lint:format", "lint:format": "npm run test:format -- --write", "lint:types": "npm run test:types -- --fix", "prepublishOnly": "npm run-s -n build test", "test": "run-s -n test:types test:only test:format", "test:format": "prettier --check '{src,test}/**/*.ts'", "test:only": "mocha", "test:types": "eslint '{src,test}/**/*.ts'"}, "types": "types/trace-mapping.d.cts", "version": "0.3.31"}