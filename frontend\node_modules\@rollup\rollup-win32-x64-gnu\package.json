{"_from": "@rollup/rollup-win32-x64-gnu@4.52.3", "_id": "@rollup/rollup-win32-x64-gnu@4.52.3", "_inBundle": false, "_integrity": "sha512-s0hybmlHb56mWVZQj8ra9048/WZTPLILKxcvcq+8awSZmyiSUZjjem1AhU3Tf4ZKpYhK4mg36HtHDOe8QJS5PQ==", "_location": "/@rollup/rollup-win32-x64-gnu", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@rollup/rollup-win32-x64-gnu@4.52.3", "name": "@rollup/rollup-win32-x64-gnu", "escapedName": "@rollup%2frollup-win32-x64-gnu", "scope": "@rollup", "rawSpec": "4.52.3", "saveSpec": null, "fetchSpec": "4.52.3"}, "_requiredBy": ["/rollup"], "_resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-gnu/-/rollup-win32-x64-gnu-4.52.3.tgz", "_shasum": "867222f288a9557487900c7836998123ebbadc9d", "_spec": "@rollup/rollup-win32-x64-gnu@4.52.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\rollup", "author": {"name": "<PERSON><PERSON>-<PERSON>"}, "bugs": {"url": "https://github.com/rollup/rollup/issues"}, "bundleDependencies": false, "cpu": ["x64"], "deprecated": false, "description": "Native bindings for Rollup", "files": ["rollup.win32-x64-gnu.node"], "homepage": "https://rollupjs.org/", "license": "MIT", "main": "./rollup.win32-x64-gnu.node", "name": "@rollup/rollup-win32-x64-gnu", "os": ["win32"], "repository": {"type": "git", "url": "git+https://github.com/rollup/rollup.git"}, "version": "4.52.3"}