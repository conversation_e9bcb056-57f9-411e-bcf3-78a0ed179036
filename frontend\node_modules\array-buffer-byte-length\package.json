{"_from": "array-buffer-byte-length@^1.0.2", "_id": "array-buffer-byte-length@1.0.2", "_inBundle": false, "_integrity": "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==", "_location": "/array-buffer-byte-length", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "array-buffer-byte-length@^1.0.2", "name": "array-buffer-byte-length", "escapedName": "array-buffer-byte-length", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/arraybuffer.prototype.slice", "/es-abstract"], "_resolved": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "_shasum": "384d12a37295aec3769ab022ad323a18a51ccf8b", "_spec": "array-buffer-byte-length@^1.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/array-buffer-byte-length/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "deprecated": false, "description": "Get the byte length of an ArrayBuffer, even in engines without a `.byteLength` method.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/array-buffer-byte-length#readme", "keywords": ["shim", "polyfill", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "byteLength", "byte", "length", "es-shim API", "es-shims"], "license": "MIT", "main": "index.js", "name": "array-buffer-byte-length", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/array-buffer-byte-length.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "types": "./index.d.ts", "version": "1.0.2"}