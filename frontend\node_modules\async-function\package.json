{"_from": "async-function@^1.0.0", "_id": "async-function@1.0.0", "_inBundle": false, "_integrity": "sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==", "_location": "/async-function", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "async-function@^1.0.0", "name": "async-function", "escapedName": "async-function", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/is-async-function"], "_resolved": "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz", "_shasum": "509c9fca60eaf85034c6829838188e4e4c8ffb2b", "_spec": "async-function@^1.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\is-async-function", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/async-function/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A function that returns the normally hidden `AsyncFunction` constructor", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/semver": "^6.2.7", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "get-proto": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": [{"module-sync": "./require.mjs", "import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/async-function#readme", "jsnext:main": "./index.mjs", "keywords": ["async", "await", "function", "native"], "license": "MIT", "main": "./legacy.js", "module": "./index.mjs", "name": "async-function", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/async-function.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">=10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.0"}