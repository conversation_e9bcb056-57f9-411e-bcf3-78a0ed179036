{"_from": "available-typed-arrays@^1.0.7", "_id": "available-typed-arrays@1.0.7", "_inBundle": false, "_integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==", "_location": "/available-typed-arrays", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "available-typed-arrays@^1.0.7", "name": "available-typed-arrays", "escapedName": "available-typed-arrays", "rawSpec": "^1.0.7", "saveSpec": null, "fetchSpec": "^1.0.7"}, "_requiredBy": ["/es-abstract", "/typed-array-byte-offset", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "_shasum": "a5cc375d6a03c2efc87a553f3e0b1522def14846", "_spec": "available-typed-arrays@^1.0.7", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/available-typed-arrays/issues"}, "bundleDependencies": false, "dependencies": {"possible-typed-array-names": "^1.0.0"}, "deprecated": false, "description": "Returns an array of Typed Array names that are available in the current environment", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/array.prototype.every": "^1.1.1", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.4", "array.prototype.every": "^1.1.5", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "isarray": "^2.0.5", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.4", "typescript": "^5.4.0-dev.20240131"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/available-typed-arrays#readme", "keywords": ["typed", "arrays", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "BigInt64Array", "BigUint64Array"], "license": "MIT", "main": "index.js", "name": "available-typed-arrays", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/available-typed-arrays.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run test:harmony", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "type": "commonjs", "types": "./index.d.ts", "version": "1.0.7"}