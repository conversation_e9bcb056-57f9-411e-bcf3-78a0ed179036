{"_from": "browserslist@^4.24.0", "_id": "browserslist@4.26.2", "_inBundle": false, "_integrity": "sha512-ECFzp6uFOSB+dcZ5BK/IBaGWssbSYBHvuMeMt3MMFyhI0Z8SqGgEkBLARgpRH3hutIgPVsALcMwbDrJqPxQ65A==", "_location": "/browserslist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "browserslist@^4.24.0", "name": "browserslist", "escapedName": "browserslist", "rawSpec": "^4.24.0", "saveSpec": null, "fetchSpec": "^4.24.0"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.26.2.tgz", "_shasum": "7db3b3577ec97f1140a52db4936654911078cef3", "_spec": "browserslist@^4.24.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\helper-compilation-targets", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"browserslist": "cli.js"}, "browser": {"./node.js": "./browser.js", "path": false}, "bugs": {"url": "https://github.com/browserslist/browserslist/issues"}, "bundleDependencies": false, "dependencies": {"baseline-browser-mapping": "^2.8.3", "caniuse-lite": "^1.0.30001741", "electron-to-chromium": "^1.5.218", "node-releases": "^2.0.21", "update-browserslist-db": "^1.1.3"}, "deprecated": false, "description": "Share target browsers between different front-end tools, like Autoprefixer, Stylelint and babel-env-preset", "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://github.com/browserslist/browserslist#readme", "keywords": ["caniuse", "browsers", "target"], "license": "MIT", "name": "browserslist", "repository": {"type": "git", "url": "git+https://github.com/browserslist/browserslist.git"}, "types": "./index.d.ts", "version": "4.26.2"}