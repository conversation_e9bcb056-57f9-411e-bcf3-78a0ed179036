{"_from": "caniuse-lite@^1.0.30001741", "_id": "caniuse-lite@1.0.30001745", "_inBundle": false, "_integrity": "sha512-ywt6i8FzvdgrrrGbr1jZVObnVv6adj+0if2/omv9cmR2oiZs30zL4DIyaptKcbOrBdOIc74QTMoJvSE2QHh5UQ==", "_location": "/caniuse-lite", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "caniuse-lite@^1.0.30001741", "name": "caniuse-lite", "escapedName": "caniuse-lite", "rawSpec": "^1.0.30001741", "saveSpec": null, "fetchSpec": "^1.0.30001741"}, "_requiredBy": ["/browserslist"], "_resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001745.tgz", "_shasum": "ab2a36e3b6ed5bfb268adc002c476aab6513f859", "_spec": "caniuse-lite@^1.0.30001741", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\browserslist", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "bugs": {"url": "https://github.com/browserslist/caniuse-lite/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A smaller version of caniuse-db, with only the essentials!", "files": ["data", "dist"], "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://github.com/browserslist/caniuse-lite#readme", "keywords": ["support"], "license": "CC-BY-4.0", "main": "dist/unpacker/index.js", "name": "caniuse-lite", "repository": {"type": "git", "url": "git+https://github.com/browserslist/caniuse-lite.git"}, "version": "1.0.30001745"}