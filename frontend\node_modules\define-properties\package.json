{"_from": "define-properties@^1.2.1", "_id": "define-properties@1.2.1", "_inBundle": false, "_integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "_location": "/define-properties", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "define-properties@^1.2.1", "name": "define-properties", "escapedName": "define-properties", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/array-includes", "/array.prototype.findlast", "/array.prototype.flat", "/array.prototype.flatmap", "/array.prototype.tosorted", "/arraybuffer.prototype.slice", "/es-iterator-helpers", "/function.prototype.name", "/globalthis", "/object.assign", "/object.entries", "/object.fromentries", "/object.values", "/reflect.getprototypeof", "/regexp.prototype.flags", "/string.prototype.matchall", "/string.prototype.repeat", "/string.prototype.trim", "/string.prototype.trimend", "/string.prototype.trimstart"], "_resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "_shasum": "10781cc616eb951a80a034bafcaa7377f6af2b6c", "_spec": "define-properties@^1.2.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\array-includes", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.1.5"}, "bugs": {"url": "https://github.com/ljharb/define-properties/issues"}, "bundleDependencies": false, "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "deprecated": false, "description": "Define multiple non-enumerable properties at once. Uses `Object.defineProperty` when available; falls back to standard assignment in older engines.", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.3", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.6.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/define-properties#readme", "keywords": ["Object.defineProperty", "Object.defineProperties", "object", "property descriptor", "descriptor", "define", "ES5"], "license": "MIT", "main": "index.js", "name": "define-properties", "publishConfig": {"ignore": [".github/workflows", "test/"]}, "repository": {"type": "git", "url": "git://github.com/ljharb/define-properties.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.2.1"}