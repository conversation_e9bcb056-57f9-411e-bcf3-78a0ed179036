{"_from": "doctrine@^3.0.0", "_id": "doctrine@3.0.0", "_inBundle": false, "_integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "_location": "/doctrine", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "doctrine@^3.0.0", "name": "doctrine", "escapedName": "doctrine", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "_shasum": "addebead72a6574db783639dc87a121773973961", "_spec": "doctrine@^3.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "bugs": {"url": "https://github.com/eslint/doctrine/issues"}, "bundleDependencies": false, "dependencies": {"esutils": "^2.0.2"}, "deprecated": false, "description": "JSDoc parser", "devDependencies": {"coveralls": "^3.0.1", "dateformat": "^1.0.11", "eslint": "^1.10.3", "eslint-release": "^1.0.0", "linefix": "^0.1.1", "mocha": "^3.4.2", "npm-license": "^0.3.1", "nyc": "^10.3.2", "semver": "^5.0.3", "shelljs": "^0.5.3", "shelljs-nodecli": "^0.1.1", "should": "^5.0.1"}, "directories": {"lib": "./lib"}, "engines": {"node": ">=6.0.0"}, "files": ["lib"], "homepage": "https://github.com/eslint/doctrine", "license": "Apache-2.0", "main": "lib/doctrine.js", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.nczonline.net"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Constellation"}], "name": "doctrine", "repository": {"type": "git", "url": "git+https://github.com/eslint/doctrine.git"}, "scripts": {"coveralls": "nyc report --reporter=text-lcov | coveralls", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "eslint lib/", "pretest": "npm run lint", "publish-release": "eslint-publish-release", "test": "nyc mocha"}, "version": "3.0.0"}