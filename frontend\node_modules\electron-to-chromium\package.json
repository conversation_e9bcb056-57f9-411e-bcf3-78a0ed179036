{"_from": "electron-to-chromium@^1.5.218", "_id": "electron-to-chromium@1.5.227", "_inBundle": false, "_integrity": "sha512-ITxuoPfJu3lsNWUi2lBM2PaBPYgH3uqmxut5vmBxgYvyI4AlJ6P3Cai1O76mOrkJCBzq0IxWg/NtqOrpu/0gKA==", "_location": "/electron-to-chromium", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "electron-to-chromium@^1.5.218", "name": "electron-to-chromium", "escapedName": "electron-to-chromium", "rawSpec": "^1.5.218", "saveSpec": null, "fetchSpec": "^1.5.218"}, "_requiredBy": ["/browserslist"], "_resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.227.tgz", "_shasum": "c81b6af045b0d6098faed261f0bd611dc282d3a7", "_spec": "electron-to-chromium@^1.5.218", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\browserslist", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/kilian/electron-to-chromium/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Provides a list of electron-to-chromium version mappings", "devDependencies": {"ava": "^5.1.1", "codecov": "^3.8.2", "compare-versions": "^6.0.0-rc.1", "node-fetch": "^3.3.0", "nyc": "^15.1.0", "shelljs": "^0.8.5"}, "files": ["versions.js", "full-versions.js", "chromium-versions.js", "full-chromium-versions.js", "versions.json", "full-versions.json", "chromium-versions.json", "full-chromium-versions.json", "LICENSE"], "homepage": "https://github.com/kilian/electron-to-chromium#readme", "keywords": ["electron", "chrome", "chromium", "browserslist", "browserlist"], "license": "ISC", "main": "index.js", "name": "electron-to-chromium", "repository": {"type": "git", "url": "git+https://github.com/kilian/electron-to-chromium.git"}, "scripts": {"build": "node build.mjs", "report": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "test": "nyc ava --verbose", "update": "node automated-update.js"}, "version": "1.5.227"}