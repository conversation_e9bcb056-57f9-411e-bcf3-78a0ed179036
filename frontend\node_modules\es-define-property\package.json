{"_from": "es-define-property@^1.0.1", "_id": "es-define-property@1.0.1", "_inBundle": false, "_integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "_location": "/es-define-property", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-define-property@^1.0.1", "name": "es-define-property", "escapedName": "es-define-property", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/call-bind", "/define-data-property", "/es-abstract", "/get-intrinsic", "/has-property-descriptors"], "_resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "_shasum": "983eb2f9a6724e9303f61addf011c72e09e0b0fa", "_spec": "es-define-property@^1.0.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/es-define-property/issues"}, "bundleDependencies": false, "deprecated": false, "description": "`Object.defineProperty`, but not IE 8's broken one.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/gopd": "^1.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "^8.8.0", "evalmd": "^0.0.19", "gopd": "^1.2.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/es-define-property#readme", "keywords": ["javascript", "ecmascript", "object", "define", "property", "defineProperty", "Object.defineProperty"], "license": "MIT", "main": "index.js", "name": "es-define-property", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-define-property.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "types": "./index.d.ts", "version": "1.0.1"}