{"_from": "es-shim-unscopables@^1.0.2", "_id": "es-shim-unscopables@1.1.0", "_inBundle": false, "_integrity": "sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==", "_location": "/es-shim-unscopables", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-shim-unscopables@^1.0.2", "name": "es-shim-unscopables", "escapedName": "es-shim-unscopables", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/array.prototype.findlast", "/array.prototype.flat", "/array.prototype.flatmap", "/array.prototype.tosorted"], "_resolved": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz", "_shasum": "438df35520dac5d105f3943d927549ea3b00f4b5", "_spec": "es-shim-unscopables@^1.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\array.prototype.findlast", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/ljharb/es-shim-unscopables/issues"}, "bundleDependencies": false, "dependencies": {"hasown": "^2.0.2"}, "deprecated": false, "description": "Helper package to shim a method into `Array.prototype[Symbol.unscopables]`", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "^5.8.0-dev.20250211"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/es-shim-unscopables#readme", "license": "MIT", "main": "index.js", "name": "es-shim-unscopables", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-shim-unscopables.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint-disabled": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.1.0"}