{"_from": "es-to-primitive@^1.3.0", "_id": "es-to-primitive@1.3.0", "_inBundle": false, "_integrity": "sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==", "_location": "/es-to-primitive", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "es-to-primitive@^1.3.0", "name": "es-to-primitive", "escapedName": "es-to-primitive", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz", "_shasum": "96c89c82cc49fd8794a24835ba3e1ff87f214e18", "_spec": "es-to-primitive@^1.3.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.2.2"}, "bugs": {"url": "https://github.com/ljharb/es-to-primitive/issues"}, "bundleDependencies": false, "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "deprecated": false, "description": "ECMAScript “ToPrimitive” algorithm. Provides ES5 and ES2015 versions.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/for-each": "^0.3.3", "@types/function.prototype.name": "^1.1.3", "@types/is-callable": "^1.1.2", "@types/is-date-object": "^1.0.4", "@types/is-symbol": "^1.0.2", "@types/object-inspect": "^1.13.0", "@types/object-is": "^1.1.0", "@types/tape": "^5.6.4", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "object-is": "^1.1.6", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/es-to-primitive#readme", "keywords": ["primitive", "abstract", "ecmascript", "es5", "es6", "es2015", "toPrimitive", "coerce", "type", "object", "string", "number", "boolean", "symbol", "null", "undefined"], "license": "MIT", "main": "index.js", "name": "es-to-primitive", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/ljharb/es-to-primitive.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.3.0"}