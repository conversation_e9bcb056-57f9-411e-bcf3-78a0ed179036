{"_from": "escalade@^3.2.0", "_id": "escalade@3.2.0", "_inBundle": false, "_integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "_location": "/escalade", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "escalade@^3.2.0", "name": "escalade", "escapedName": "escalade", "rawSpec": "^3.2.0", "saveSpec": null, "fetchSpec": "^3.2.0"}, "_requiredBy": ["/update-browserslist-db"], "_resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "_shasum": "011a3f69856ba189dffa7dc8fcce99d2a87903e5", "_spec": "escalade@^3.2.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\update-browserslist-db", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "bugs": {"url": "https://github.com/lukeed/escalade/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A tiny (183B to 210B) and fast utility to ascend parent directories", "devDependencies": {"bundt": "1.1.1", "esm": "3.2.25", "uvu": "0.3.3"}, "engines": {"node": ">=6"}, "exports": {".": [{"import": {"types": "./index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./index.d.ts", "default": "./dist/index.js"}}, "./dist/index.js"], "./sync": [{"import": {"types": "./sync/index.d.mts", "default": "./sync/index.mjs"}, "require": {"types": "./sync/index.d.ts", "default": "./sync/index.js"}}, "./sync/index.js"]}, "files": ["*.d.mts", "*.d.ts", "dist", "sync"], "homepage": "https://github.com/lukeed/escalade#readme", "keywords": ["find", "parent", "parents", "directory", "search", "walk"], "license": "MIT", "main": "dist/index.js", "modes": {"sync": "src/sync.js", "default": "src/async.js"}, "module": "dist/index.mjs", "name": "escalade", "repository": {"type": "git", "url": "git+https://github.com/lukeed/escalade.git"}, "scripts": {"build": "bundt", "pretest": "npm run build", "test": "uvu -r esm test -i fixtures"}, "types": "index.d.ts", "version": "3.2.0"}