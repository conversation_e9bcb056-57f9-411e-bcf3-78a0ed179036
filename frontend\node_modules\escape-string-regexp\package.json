{"_from": "escape-string-regexp@^4.0.0", "_id": "escape-string-regexp@4.0.0", "_inBundle": false, "_integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "_location": "/escape-string-regexp", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "escape-string-regexp@^4.0.0", "name": "escape-string-regexp", "escapedName": "escape-string-regexp", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "_shasum": "14ba83a5d373e3d311e5afca29cf5bfad965bf34", "_spec": "escape-string-regexp@^4.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/escape-string-regexp/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Escape RegExp special characters", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/escape-string-regexp#readme", "keywords": ["escape", "regex", "regexp", "regular", "expression", "string", "special", "characters"], "license": "MIT", "name": "escape-string-regexp", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/escape-string-regexp.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.0"}