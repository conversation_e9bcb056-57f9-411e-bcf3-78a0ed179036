{"_from": "eslint-scope@^7.2.2", "_id": "eslint-scope@7.2.2", "_inBundle": false, "_integrity": "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==", "_location": "/eslint-scope", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "eslint-scope@^7.2.2", "name": "eslint-scope", "escapedName": "eslint-scope", "rawSpec": "^7.2.2", "saveSpec": null, "fetchSpec": "^7.2.2"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "_shasum": "deb4f92563390f32006894af62a22dba1c46423f", "_spec": "eslint-scope@^7.2.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "bundleDependencies": false, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "deprecated": false, "description": "ECMAScript scope analyzer for ESLint", "devDependencies": {"@typescript-eslint/parser": "^4.28.1", "c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "mocha": "^9.0.1", "npm-license": "^0.3.3", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./lib/index.js", "require": "./dist/eslint-scope.cjs"}, "./package.json": "./package.json"}, "files": ["LICENSE", "README.md", "lib", "dist/eslint-scope.cjs"], "funding": "https://opencollective.com/eslint", "homepage": "http://github.com/eslint/eslint-scope", "license": "BSD-2-<PERSON><PERSON>", "main": "./dist/eslint-scope.cjs", "name": "eslint-scope", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint-scope.git"}, "scripts": {"build": "rollup -c", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "npm run build && node Makefile.js lint", "prepublishOnly": "npm run update-version && npm run build", "publish-release": "eslint-publish-release", "test": "npm run build && node Makefile.js test", "update-version": "node tools/update-version.js"}, "type": "module", "version": "7.2.2"}