{"_from": "espree@^9.6.1", "_id": "espree@9.6.1", "_inBundle": false, "_integrity": "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==", "_location": "/espree", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "espree@^9.6.1", "name": "espree", "escapedName": "espree", "rawSpec": "^9.6.1", "saveSpec": null, "fetchSpec": "^9.6.1"}, "_requiredBy": ["/@eslint/eslintrc", "/eslint"], "_resolved": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "_shasum": "a2a17b8e434690a5432f2f8018ce71d331a48c6f", "_spec": "espree@^9.6.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/eslint/espree/issues"}, "bundleDependencies": false, "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "deprecated": false, "description": "An Esprima-compatible JavaScript parser built on Acorn", "devDependencies": {"@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "c8": "^7.11.0", "chai": "^4.3.6", "eslint": "^8.44.0", "eslint-config-eslint": "^8.0.0", "eslint-plugin-n": "^16.0.0", "eslint-release": "^3.2.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "globals": "^13.20.0", "lint-staged": "^13.2.0", "mocha": "^9.2.2", "npm-run-all": "^4.1.5", "rollup": "^2.41.2", "shelljs": "^0.3.0", "yorkie": "^2.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./espree.js", "require": "./dist/espree.cjs", "default": "./dist/espree.cjs"}, "./dist/espree.cjs"], "./package.json": "./package.json"}, "files": ["lib", "dist/espree.cjs", "espree.js"], "funding": "https://opencollective.com/eslint", "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://github.com/eslint/espree", "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax", "acorn"], "license": "BSD-2-<PERSON><PERSON>", "main": "dist/espree.cjs", "name": "espree", "repository": {"type": "git", "url": "git+https://github.com/eslint/espree.git"}, "scripts": {"build": "rollup -c rollup.config.js", "build:debug": "npm run build -- -m", "fixlint": "npm run lint -- --fix", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "eslint .  --report-unused-disable-directives", "prepublishOnly": "npm run update-version && npm run build", "pretest": "npm run build", "publish-release": "eslint-publish-release", "sync-docs": "node sync-docs.js", "test": "npm-run-all -p unit lint", "unit": "npm-run-all -s unit:*", "unit:cjs": "mocha --color --reporter progress --timeout 30000 tests/lib/commonjs.cjs", "unit:esm": "c8 mocha --color --reporter progress --timeout 30000 'tests/lib/**/*.js'", "update-version": "node tools/update-version.js"}, "type": "module", "version": "9.6.1"}