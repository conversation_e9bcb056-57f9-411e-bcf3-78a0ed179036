{"_from": "esrecurse@^4.3.0", "_id": "esrecurse@4.3.0", "_inBundle": false, "_integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "_location": "/esrecurse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "esrecurse@^4.3.0", "name": "esrecurse", "escapedName": "esrecurse", "rawSpec": "^4.3.0", "saveSpec": null, "fetchSpec": "^4.3.0"}, "_requiredBy": ["/eslint-scope"], "_resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "_shasum": "7ad7964d679abb28bee72cec63758b1c5d2c9921", "_spec": "esrecurse@^4.3.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint-scope", "babel": {"presets": ["es2015"]}, "bugs": {"url": "https://github.com/estools/esrecurse/issues"}, "bundleDependencies": false, "dependencies": {"estraverse": "^5.2.0"}, "deprecated": false, "description": "ECMAScript AST recursive visitor", "devDependencies": {"babel-cli": "^6.24.1", "babel-eslint": "^7.2.3", "babel-preset-es2015": "^6.24.1", "babel-register": "^6.24.1", "chai": "^4.0.2", "esprima": "^4.0.0", "gulp": "^3.9.0", "gulp-bump": "^2.7.0", "gulp-eslint": "^4.0.0", "gulp-filter": "^5.0.0", "gulp-git": "^2.4.1", "gulp-mocha": "^4.3.1", "gulp-tag-version": "^1.2.1", "jsdoc": "^3.3.0-alpha10", "minimist": "^1.1.0"}, "engines": {"node": ">=4.0"}, "homepage": "https://github.com/estools/esrecurse", "license": "BSD-2-<PERSON><PERSON>", "main": "esrecurse.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/Constellation"}], "name": "esrecurse", "repository": {"type": "git", "url": "git+https://github.com/estools/esrecurse.git"}, "scripts": {"lint": "gulp lint", "test": "gulp travis", "unit-test": "gulp test"}, "version": "4.3.0"}