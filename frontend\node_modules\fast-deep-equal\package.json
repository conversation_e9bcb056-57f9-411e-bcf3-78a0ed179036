{"_from": "fast-deep-equal@^3.1.3", "_id": "fast-deep-equal@3.1.3", "_inBundle": false, "_integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "_location": "/fast-deep-equal", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fast-deep-equal@^3.1.3", "name": "fast-deep-equal", "escapedName": "fast-deep-equal", "rawSpec": "^3.1.3", "saveSpec": null, "fetchSpec": "^3.1.3"}, "_requiredBy": ["/ajv", "/eslint"], "_resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "_shasum": "3a7d56b559d6cbc3eb512325244e619a65c6c525", "_spec": "fast-deep-equal@^3.1.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/epoberezkin/fast-deep-equal/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Fast deep equal", "devDependencies": {"coveralls": "^3.1.0", "dot": "^1.1.2", "eslint": "^7.2.0", "mocha": "^7.2.0", "nyc": "^15.1.0", "pre-commit": "^1.2.2", "react": "^16.12.0", "react-test-renderer": "^16.12.0", "sinon": "^9.0.2", "typescript": "^3.9.5"}, "files": ["index.js", "index.d.ts", "react.js", "react.d.ts", "es6/"], "homepage": "https://github.com/epoberezkin/fast-deep-equal#readme", "keywords": ["fast", "equal", "deep-equal"], "license": "MIT", "main": "index.js", "name": "fast-deep-equal", "nyc": {"exclude": ["**/spec/**", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "git+https://github.com/epoberezkin/fast-deep-equal.git"}, "scripts": {"benchmark": "npm i && npm run build && cd ./benchmark && npm i && node ./", "build": "node build", "eslint": "eslint *.js benchmark/*.js spec/*.js", "prepublish": "npm run build", "test": "npm run build && npm run eslint && npm run test-ts && npm run test-cov", "test-cov": "nyc npm run test-spec", "test-spec": "mocha spec/*.spec.js -R spec", "test-ts": "tsc --target ES5 --noImplicitAny index.d.ts"}, "types": "index.d.ts", "version": "3.1.3"}