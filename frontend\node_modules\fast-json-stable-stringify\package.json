{"_from": "fast-json-stable-stringify@^2.0.0", "_id": "fast-json-stable-stringify@2.1.0", "_inBundle": false, "_integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "_location": "/fast-json-stable-stringify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fast-json-stable-stringify@^2.0.0", "name": "fast-json-stable-stringify", "escapedName": "fast-json-stable-stringify", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/ajv"], "_resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "_shasum": "874bf69c6f404c2b5d99c481341399fd55892633", "_spec": "fast-json-stable-stringify@^2.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\ajv", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/epoberezkin/fast-json-stable-stringify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "deterministic `JSON.stringify()` - a faster version of substack's json-stable-strigify without jsonify", "devDependencies": {"benchmark": "^2.1.4", "coveralls": "^3.0.0", "eslint": "^6.7.0", "fast-stable-stringify": "latest", "faster-stable-stringify": "latest", "json-stable-stringify": "latest", "nyc": "^14.1.0", "pre-commit": "^1.2.2", "tape": "^4.11.0"}, "homepage": "https://github.com/epoberezkin/fast-json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "stable"], "license": "MIT", "main": "index.js", "name": "fast-json-stable-stringify", "nyc": {"exclude": ["test", "node_modules"], "reporter": ["lcov", "text-summary"]}, "repository": {"type": "git", "url": "git://github.com/epoberezkin/fast-json-stable-stringify.git"}, "scripts": {"eslint": "eslint index.js test", "test": "npm run eslint && nyc npm run test-spec", "test-spec": "tape test/*.js"}, "types": "index.d.ts", "version": "2.1.0"}