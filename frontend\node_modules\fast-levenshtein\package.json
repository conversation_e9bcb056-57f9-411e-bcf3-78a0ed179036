{"_from": "fast-le<PERSON><PERSON><PERSON>@^2.0.6", "_id": "fast-le<PERSON><PERSON><PERSON>@2.0.6", "_inBundle": false, "_integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "_location": "/fast-le<PERSON><PERSON><PERSON>", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fast-le<PERSON><PERSON><PERSON>@^2.0.6", "name": "fast-le<PERSON><PERSON><PERSON>", "escapedName": "fast-le<PERSON><PERSON><PERSON>", "rawSpec": "^2.0.6", "saveSpec": null, "fetchSpec": "^2.0.6"}, "_requiredBy": ["/optionator"], "_resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "_shasum": "3d8a5c66883a16a30ca8643e851f19baa7797917", "_spec": "fast-le<PERSON><PERSON><PERSON>@^2.0.6", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\optionator", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.hiddentao.com/"}, "bugs": {"url": "https://github.com/hiddentao/fast-levenshtein/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Efficient implementation of Levenshtein algorithm  with locale-specific collator support.", "devDependencies": {"chai": "~1.5.0", "grunt": "~0.4.1", "grunt-benchmark": "~0.2.0", "grunt-cli": "^1.2.0", "grunt-contrib-jshint": "~0.4.3", "grunt-contrib-uglify": "~0.2.0", "grunt-mocha-test": "~0.2.2", "grunt-npm-install": "~0.1.0", "load-grunt-tasks": "~0.6.0", "lodash": "^4.0.1", "mocha": "~1.9.0"}, "files": ["levenshtein.js"], "homepage": "https://github.com/hiddentao/fast-levenshtein#readme", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "distance", "string"], "license": "MIT", "main": "levenshtein.js", "name": "fast-le<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/hiddentao/fast-levenshtein.git"}, "scripts": {"benchmark": "grunt benchmark", "build": "grunt build", "prepublish": "npm run build", "test": "mocha"}, "version": "2.0.6"}