{"_from": "for-each@^0.3.5", "_id": "for-each@0.3.5", "_inBundle": false, "_integrity": "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==", "_location": "/for-each", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "for-each@^0.3.5", "name": "for-each", "escapedName": "for-each", "rawSpec": "^0.3.5", "saveSpec": null, "fetchSpec": "^0.3.5"}, "_requiredBy": ["/typed-array-byte-length", "/typed-array-byte-offset", "/typed-array-length", "/which-typed-array"], "_resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz", "_shasum": "d650688027826920feeb0af747ee7b9421a41d47", "_spec": "for-each@^0.3.5", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-typed-array", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/Raynos/for-each/issues", "email": "<EMAIL>"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "dependencies": {"is-callable": "^1.2.7"}, "deprecated": false, "description": "A better for<PERSON>ach", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/is-callable": "^1.1.2", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/Raynos/for-each", "keywords": [], "license": "MIT", "main": "index", "name": "for-each", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/Raynos/for-each.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/test.js"}, "version": "0.3.5"}