{"_from": "fs.realpath@^1.0.0", "_id": "fs.realpath@1.0.0", "_inBundle": false, "_integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "_location": "/fs.realpath", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "fs.realpath@^1.0.0", "name": "fs.realpath", "escapedName": "fs.realpath", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/glob"], "_resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "_shasum": "1504ad2523158caa40db4a2787cb01411994ea4f", "_spec": "fs.realpath@^1.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\glob", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/fs.realpath/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "devDependencies": {}, "files": ["old.js", "index.js"], "homepage": "https://github.com/isaacs/fs.realpath#readme", "keywords": ["realpath", "fs", "polyfill"], "license": "ISC", "main": "index.js", "name": "fs.realpath", "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "scripts": {"test": "tap test/*.js --cov"}, "version": "1.0.0"}