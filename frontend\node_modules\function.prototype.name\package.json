{"_from": "function.prototype.name@^1.1.8", "_id": "function.prototype.name@1.1.8", "_inBundle": false, "_integrity": "sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==", "_location": "/function.prototype.name", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "function.prototype.name@^1.1.8", "name": "function.prototype.name", "escapedName": "function.prototype.name", "rawSpec": "^1.1.8", "saveSpec": null, "fetchSpec": "^1.1.8"}, "_requiredBy": ["/es-abstract", "/which-builtin-type"], "_resolved": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "_shasum": "e68e1df7b259a5c949eeef95cdbde53edffabb78", "_spec": "function.prototype.name@^1.1.8", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "v1.1.6"}, "bugs": {"url": "https://github.com/es-shims/Function.prototype.name/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "deprecated": false, "description": "An ES2015 spec-compliant `Function.prototype.name` shim", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "uglify-register": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/Function.prototype.name#readme", "keywords": ["Function.prototype.name", "function", "name", "ES6", "ES2015", "shim", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "name": "function.prototype.name", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/es-shims/Function.prototype.name.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run --silent tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.8"}