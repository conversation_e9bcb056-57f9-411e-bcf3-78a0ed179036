{"_from": "functions-have-names@^1.2.3", "_id": "functions-have-names@1.2.3", "_inBundle": false, "_integrity": "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==", "_location": "/functions-have-names", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "functions-have-names@^1.2.3", "name": "functions-have-names", "escapedName": "functions-have-names", "rawSpec": "^1.2.3", "saveSpec": null, "fetchSpec": "^1.2.3"}, "_requiredBy": ["/function.prototype.name", "/set-function-name"], "_resolved": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz", "_shasum": "0404fe4ee2ba2f607f0e0ec3c80bae994133b834", "_spec": "functions-have-names@^1.2.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\function.prototype.name", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/functions-have-names/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Does this JS environment support the `name` property on functions?", "devDependencies": {"@ljharb/eslint-config": "^21.0.0", "aud": "^2.0.0", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.5.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/functions-have-names#readme", "keywords": ["function", "name", "es5", "names", "functions", "ie"], "license": "MIT", "main": "index.js", "name": "functions-have-names", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/functions-have-names.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.2.3"}