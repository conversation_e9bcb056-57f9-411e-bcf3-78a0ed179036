{"_from": "gensync@^1.0.0-beta.2", "_id": "gensync@1.0.0-beta.2", "_inBundle": false, "_integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "_location": "/gensync", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "gensync@^1.0.0-beta.2", "name": "gens<PERSON>", "escapedName": "gens<PERSON>", "rawSpec": "^1.0.0-beta.2", "saveSpec": null, "fetchSpec": "^1.0.0-beta.2"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "_shasum": "32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0", "_spec": "gensync@^1.0.0-beta.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/loganfsmyth/gensync/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Allows users to use generators in order to write common functions that can be both sync or async.", "devDependencies": {"babel-core": "^6.26.3", "babel-preset-env": "^1.6.1", "eslint": "^4.19.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-node": "^6.0.1", "eslint-plugin-prettier": "^2.6.0", "flow-bin": "^0.71.0", "jest": "^22.4.3", "prettier": "^1.12.1"}, "engines": {"node": ">=6.9.0"}, "homepage": "https://github.com/loganfsmyth/gensync", "keywords": ["async", "sync", "generators", "async-await", "callbacks"], "license": "MIT", "main": "index.js", "name": "gens<PERSON>", "repository": {"type": "git", "url": "git+https://github.com/loganfsmyth/gensync.git"}, "scripts": {"test": "jest"}, "version": "1.0.0-beta.2"}