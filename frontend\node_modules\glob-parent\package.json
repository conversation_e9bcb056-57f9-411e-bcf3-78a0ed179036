{"_from": "glob-parent@^6.0.2", "_id": "glob-parent@6.0.2", "_inBundle": false, "_integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "_location": "/glob-parent", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "glob-parent@^6.0.2", "name": "glob-parent", "escapedName": "glob-parent", "rawSpec": "^6.0.2", "saveSpec": null, "fetchSpec": "^6.0.2"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "_shasum": "6d237d99083950c79290f24c7642a3de9a28f9e3", "_spec": "glob-parent@^6.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "https://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/glob-parent/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"is-glob": "^4.0.3"}, "deprecated": false, "description": "Extract the non-magic parent path from a glob string.", "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "expect": "^26.0.1", "mocha": "^7.1.2", "nyc": "^15.0.1"}, "engines": {"node": ">=10.13.0"}, "files": ["LICENSE", "index.js"], "homepage": "https://github.com/gulpjs/glob-parent#readme", "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "license": "ISC", "main": "index.js", "name": "glob-parent", "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/gulpjs/glob-parent.git"}, "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "version": "6.0.2"}