{"_from": "globals@^13.19.0", "_id": "globals@13.24.0", "_inBundle": false, "_integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==", "_location": "/globals", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "globals@^13.19.0", "name": "globals", "escapedName": "globals", "rawSpec": "^13.19.0", "saveSpec": null, "fetchSpec": "^13.19.0"}, "_requiredBy": ["/@eslint/eslintrc", "/eslint"], "_resolved": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "_shasum": "8432a19d78ce0c1e833949c36adb345400bb1171", "_spec": "globals@^13.19.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/globals/issues"}, "bundleDependencies": false, "dependencies": {"type-fest": "^0.20.2"}, "deprecated": false, "description": "Global identifiers from different JavaScript environments", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.14.0", "xo": "^0.36.1"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts", "globals.json"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/globals#readme", "keywords": ["globals", "global", "identifiers", "variables", "vars", "j<PERSON>t", "eslint", "environments"], "license": "MIT", "name": "globals", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/globals.git"}, "scripts": {"test": "xo && ava"}, "sideEffects": false, "tsd": {"compilerOptions": {"resolveJsonModule": true}}, "version": "13.24.0", "xo": {"ignores": ["get-browser-globals.js"], "rules": {"node/no-unsupported-features/es-syntax": "off"}}}