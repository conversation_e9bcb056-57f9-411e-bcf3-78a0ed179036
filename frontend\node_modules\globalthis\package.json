{"_from": "globalthis@^1.0.4", "_id": "globalthis@1.0.4", "_inBundle": false, "_integrity": "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==", "_location": "/globalthis", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "globalthis@^1.0.4", "name": "globalthis", "escapedName": "globalthis", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["/es-abstract", "/es-iterator-helpers"], "_resolved": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz", "_shasum": "7430ed3a975d97bfb59bcce41f5cabbafa651236", "_spec": "globalthis@^1.0.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "browser": {"./implementation": "./implementation.browser.js"}, "bugs": {"url": "https://github.com/ljharb/System.global/issues"}, "bundleDependencies": false, "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "deprecated": false, "description": "ECMAScript spec-compliant polyfill/shim for `globalThis`", "devDependencies": {"@es-shims/api": "^2.5.0", "@ljharb/eslint-config": "^21.1.0", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "browserify": "^16.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "is": "^3.3.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/System.global#readme", "keywords": ["window", "self", "global", "globalThis", "System.global", "global object", "global this value", "ECMAScript", "es-shim API", "polyfill", "shim"], "license": "MIT", "main": "index.js", "name": "globalthis", "publishConfig": {"ignore": ["browserShim.js", ".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/ljharb/System.global.git"}, "scripts": {"build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound --property", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run build", "pretest": "npm run lint", "test": "npm run --silent tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.0.4"}