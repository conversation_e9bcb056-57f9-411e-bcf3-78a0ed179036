{"_from": "graphemer@^1.4.0", "_id": "graphemer@1.4.0", "_inBundle": false, "_integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==", "_location": "/graphemer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "graphemer@^1.4.0", "name": "graphemer", "escapedName": "graphemer", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "_shasum": "fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6", "_spec": "graphemer@^1.4.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/mattpauldavies"}, "bugs": {"url": "https://github.com/flmnt/graphemer/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/orling"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/JLHwung"}], "deprecated": false, "description": "A JavaScript library that breaks strings into their individual user-perceived characters (including emojis!)", "devDependencies": {"@types/tape": "^4.13.0", "husky": "^4.3.0", "lint-staged": "^10.3.0", "prettier": "^2.1.1", "tape": "^4.6.3", "ts-node": "^9.0.0", "typescript": "^4.0.2"}, "files": ["lib"], "homepage": "https://github.com/flmnt/graphemer", "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}, "keywords": ["utf-8", "strings", "emoji", "split"], "license": "MIT", "lint-staged": {"*.{js,ts,md,json}": "prettier --write"}, "main": "./lib/index.js", "name": "graphemer", "repository": {"type": "git", "url": "git+https://github.com/flmnt/graphemer.git"}, "scripts": {"build": "tsc --project tsconfig.json", "prepublishOnly": "npm run build", "pretest": "npm run build", "prettier:check": "prettier --check .", "prettier:fix": "prettier --write .", "test": "ts-node node_modules/tape/bin/tape tests/**.ts"}, "types": "./lib/index.d.ts", "version": "1.4.0"}