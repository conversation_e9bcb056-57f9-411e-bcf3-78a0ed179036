{"_from": "has-bigints@^1.0.2", "_id": "has-bigints@1.1.0", "_inBundle": false, "_integrity": "sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==", "_location": "/has-bigints", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-bigints@^1.0.2", "name": "has-bigints", "escapedName": "has-bigints", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/is-bigint", "/unbox-primitive"], "_resolved": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz", "_shasum": "28607e965ac967e03cd2a2c70a2636a1edad49fe", "_spec": "has-bigints@^1.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\is-bigint", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/has-bigints/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Determine if the JS environment has BigInt support.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/has-bigints#readme", "keywords": ["BigInt", "bigints", "typeof", "ES2020"], "license": "MIT", "main": "index.js", "name": "has-bigints", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/has-bigints.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.1.0"}