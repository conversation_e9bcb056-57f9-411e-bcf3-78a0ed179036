{"_from": "has-symbols@^1.1.0", "_id": "has-symbols@1.1.0", "_inBundle": false, "_integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "_location": "/has-symbols", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "has-symbols@^1.1.0", "name": "has-symbols", "escapedName": "has-symbols", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/es-abstract", "/es-iterator-helpers", "/get-intrinsic", "/has-tostringtag", "/is-symbol", "/iterator.prototype", "/object.assign", "/safe-array-concat", "/string.prototype.matchall", "/unbox-primitive"], "_resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "_shasum": "fc9c6a783a084951d0b971fe1018de813707a338", "_spec": "has-symbols@^1.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/has-symbols/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "deprecated": false, "description": "Determine if the JS environment has Symbol support. Supports spec, or shams.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.0", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.0", "@types/core-js": "^2.5.8", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "core-js": "^2.6.12", "encoding": "^0.1.13", "eslint": "=8.8.0", "get-own-property-symbols": "^0.9.5", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/has-symbols#readme", "keywords": ["Symbol", "symbols", "typeof", "sham", "polyfill", "native", "core-js", "ES6"], "license": "MIT", "main": "index.js", "name": "has-symbols", "publishConfig": {"ignore": [".github/workflows", "types"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/has-symbols.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "test:shams": "npm run --silent test:shams:getownpropertysymbols && npm run --silent test:shams:corejs", "test:shams:corejs": "nyc node test/shams/core-js.js", "test:shams:getownpropertysymbols": "nyc node test/shams/get-own-property-symbols.js", "test:staging": "nyc node --harmony --es-staging test", "test:stock": "nyc node test", "tests-only": "npm run test:stock && npm run test:shams", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.0"}