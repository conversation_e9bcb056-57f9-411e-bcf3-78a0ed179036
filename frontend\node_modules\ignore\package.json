{"_from": "ignore@^5.2.0", "_id": "ignore@5.3.2", "_inBundle": false, "_integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "_location": "/ignore", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ignore@^5.2.0", "name": "ignore", "escapedName": "ignore", "rawSpec": "^5.2.0", "saveSpec": null, "fetchSpec": "^5.2.0"}, "_requiredBy": ["/@eslint/eslintrc", "/eslint"], "_resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "_shasum": "3cd40e729f3643fd87cb04e50bf0eb722bc596f5", "_spec": "ignore@^5.2.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "kael"}, "bugs": {"url": "https://github.com/kaelzhang/node-ignore/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Ignore is a manager and filter for .gitignore rules, the one used by eslint, gitbook and many others.", "devDependencies": {"@babel/cli": "^7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "codecov": "^3.8.2", "debug": "^4.3.4", "eslint": "^8.46.0", "eslint-config-ostai": "^3.0.0", "eslint-plugin-import": "^2.28.0", "mkdirp": "^3.0.1", "pre-suf": "^1.1.1", "rimraf": "^6.0.1", "spawn-sync": "^2.0.0", "tap": "^16.3.9", "tmp": "0.2.3", "typescript": "^5.1.6"}, "engines": {"node": ">= 4"}, "files": ["legacy.js", "index.js", "index.d.ts", "LICENSE-MIT"], "homepage": "https://github.com/kaelzhang/node-ignore#readme", "keywords": ["ignore", ".giti<PERSON>re", "gitignore", "npmignore", "rules", "manager", "filter", "regexp", "regex", "fnmatch", "glob", "asterisks", "regular-expression"], "license": "MIT", "name": "ignore", "repository": {"type": "git", "url": "git+ssh://**************/kaelzhang/node-ignore.git"}, "scripts": {"build": "babel -o legacy.js index.js", "posttest": "npm run report && codecov", "prepublishOnly": "npm run build", "report": "tap --coverage-report=html", "tap": "tap --reporter classic", "test": "npm run test:only", "test:cases": "npm run tap test/*.js -- --coverage", "test:git": "npm run tap test/git-check-ignore.js", "test:ignore": "npm run tap test/ignore.js", "test:ignore:only": "IGNORE_ONLY_IGNORES=1 npm run tap test/ignore.js", "test:lint": "eslint .", "test:no-coverage": "npm run tap test/*.js -- --no-check-coverage", "test:only": "npm run test:lint && npm run test:tsc && npm run test:ts && npm run test:cases", "test:others": "npm run tap test/others.js", "test:ts": "node ./test/ts/simple.js", "test:tsc": "tsc ./test/ts/simple.ts --lib ES6", "test:win32": "IGNORE_TEST_WIN32=1 npm run test"}, "version": "5.3.2"}