{"_from": "import-fresh@^3.2.1", "_id": "import-fresh@3.3.1", "_inBundle": false, "_integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==", "_location": "/import-fresh", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "import-fresh@^3.2.1", "name": "import-fresh", "escapedName": "import-fresh", "rawSpec": "^3.2.1", "saveSpec": null, "fetchSpec": "^3.2.1"}, "_requiredBy": ["/@eslint/eslintrc"], "_resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "_shasum": "9cecb56503c0ada1f2741dbbd6546e4b13b57ccf", "_spec": "import-fresh@^3.2.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@eslint\\eslintrc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/import-fresh/issues"}, "bundleDependencies": false, "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "deprecated": false, "description": "Import a module while bypassing the cache", "devDependencies": {"ava": "^1.0.1", "heapdump": "^0.3.12", "tsd": "^0.7.3", "xo": "^0.23.0"}, "engines": {"node": ">=6"}, "exports": {"types": "./index.d.ts", "default": "./index.js"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/import-fresh#readme", "keywords": ["require", "cache", "uncache", "uncached", "module", "fresh", "bypass"], "license": "MIT", "name": "import-fresh", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/import-fresh.git"}, "scripts": {"heapdump": "node heapdump.js", "test": "xo && ava && tsd"}, "sideEffects": false, "version": "3.3.1"}