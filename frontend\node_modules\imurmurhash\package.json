{"_from": "imur<PERSON><PERSON><PERSON>h@^0.1.4", "_id": "im<PERSON><PERSON><PERSON><PERSON><PERSON>@0.1.4", "_inBundle": false, "_integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "_location": "/imurmur<PERSON>h", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "imur<PERSON><PERSON><PERSON>h@^0.1.4", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rawSpec": "^0.1.4", "saveSpec": null, "fetchSpec": "^0.1.4"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "_shasum": "9218b9b2b928a238b13dc4fb6b6d576f231453ea", "_spec": "imur<PERSON><PERSON><PERSON>h@^0.1.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/homebrewing"}, "bugs": {"url": "https://github.com/jensyt/imurmurhash-js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "An incremental implementation of MurmurHash3", "devDependencies": {}, "engines": {"node": ">=0.8.19"}, "files": ["imurmurhash.js", "imurmurhash.min.js", "package.json", "README.md"], "homepage": "https://github.com/jensyt/imurmurhash-js", "keywords": ["murmur", "<PERSON><PERSON>h", "murmurhash3", "hash", "incremental"], "license": "MIT", "main": "imurmurhash.js", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/jensyt/imurmurhash-js.git"}, "version": "0.1.4"}