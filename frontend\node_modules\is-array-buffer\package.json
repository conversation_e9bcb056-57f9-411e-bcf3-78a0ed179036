{"_from": "is-array-buffer@^3.0.5", "_id": "is-array-buffer@3.0.5", "_inBundle": false, "_integrity": "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==", "_location": "/is-array-buffer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-array-buffer@^3.0.5", "name": "is-array-buffer", "escapedName": "is-array-buffer", "rawSpec": "^3.0.5", "saveSpec": null, "fetchSpec": "^3.0.5"}, "_requiredBy": ["/array-buffer-byte-length", "/arraybuffer.prototype.slice", "/es-abstract"], "_resolved": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "_shasum": "65742e1e687bd2cc666253068fd8707fe4d44280", "_spec": "is-array-buffer@^3.0.5", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "2.0.1"}, "bugs": {"url": "https://github.com/inspect-js/is-array-buffer/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "deprecated": false, "description": "Is this value a JS ArrayBuffer?", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/for-each": "^0.3.3", "@types/get-intrinsic": "^1.2.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-array-buffer#readme", "keywords": ["javascript", "ecmascript", "is", "arraybuffer", "array", "buffer"], "license": "MIT", "main": "index.js", "name": "is-array-buffer", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-array-buffer.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only --", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "types": "./index.d.ts", "version": "3.0.5"}