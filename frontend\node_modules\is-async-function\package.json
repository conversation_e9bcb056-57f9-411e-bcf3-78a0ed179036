{"_from": "is-async-function@^2.0.0", "_id": "is-async-function@2.1.1", "_inBundle": false, "_integrity": "sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==", "_location": "/is-async-function", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-async-function@^2.0.0", "name": "is-async-function", "escapedName": "is-async-function", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/which-builtin-type"], "_resolved": "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz", "_shasum": "3e69018c8e04e73b738793d020bfe884b9fd3523", "_spec": "is-async-function@^2.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-builtin-type", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-async-function/issues"}, "bundleDependencies": false, "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "deprecated": false, "description": "Determine if a function is a native async function.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/make-async-function": "^1.0.2", "@types/make-generator-function": "^2.0.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "make-async-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next", "uglify-register": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-async-function#readme", "keywords": ["async", "async function", "es6", "es2015", "yield", "function", "function*"], "license": "MIT", "main": "index.js", "name": "is-async-function", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-async-function.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "test:all": "npm run test:index && npm run test:uglified", "test:index": "node test", "test:uglified": "node test/uglified", "tests-only": "nyc npm run test:all", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "2.1.1"}