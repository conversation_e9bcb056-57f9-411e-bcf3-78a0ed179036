{"_from": "is-data-view@^1.0.2", "_id": "is-data-view@1.0.2", "_inBundle": false, "_integrity": "sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==", "_location": "/is-data-view", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-data-view@^1.0.2", "name": "is-data-view", "escapedName": "is-data-view", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/data-view-buffer", "/data-view-byte-length", "/data-view-byte-offset", "/es-abstract"], "_resolved": "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz", "_shasum": "bae0a41b9688986c2188dda6657e56b8f9e63b8e", "_spec": "is-data-view@^1.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-data-view/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "deprecated": false, "description": "Is this value a JS DataView? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and despite ES6 Symbol.toStringTag.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/node": "^20.17.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-data-view#readme", "keywords": ["javascript", "ecmascript", "dataview", "data", "view", "typedarray", "typedarrays"], "license": "MIT", "main": "index.js", "name": "is-data-view", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-data-view.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "types": "./index.d.ts", "version": "1.0.2"}