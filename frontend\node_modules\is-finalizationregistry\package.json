{"_from": "is-finalizationregistry@^1.1.0", "_id": "is-finalizationregistry@1.1.1", "_inBundle": false, "_integrity": "sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==", "_location": "/is-finalizationregistry", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-finalizationregistry@^1.1.0", "name": "is-finalizationregistry", "escapedName": "is-finalizationregistry", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/which-builtin-type"], "_resolved": "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz", "_shasum": "eefdcdc6c94ddd0674d9c85887bf93f944a97c90", "_spec": "is-finalizationregistry@^1.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-builtin-type", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-finalizationregistry/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.3"}, "deprecated": false, "description": "Is this value a JS FinalizationRegistry? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-finalizationregistry#readme", "keywords": ["weakref", "finalization", "finalizationregistry", "finalization registry"], "license": "MIT", "main": "index.js", "name": "is-finalizationregistry", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-finalizationregistry.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -P . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": true, "types": "index.d.ts", "version": "1.1.1"}