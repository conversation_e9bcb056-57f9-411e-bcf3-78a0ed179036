{"_from": "is-map@^2.0.3", "_id": "is-map@2.0.3", "_inBundle": false, "_integrity": "sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==", "_location": "/is-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-map@^2.0.3", "name": "is-map", "escapedName": "is-map", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/which-collection"], "_resolved": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz", "_shasum": "ede96b7fe1e270b3c4465e3a465658764926d62e", "_spec": "is-map@^2.0.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-collection", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-map/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Is this value a JS Map? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "^5.5.0-dev.20240308"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-map#readme", "keywords": ["map", "set", "collection", "is", "robust"], "license": "MIT", "main": "index.js", "name": "is-map", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-map.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "tests-only": "nyc tape 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "2.0.3"}