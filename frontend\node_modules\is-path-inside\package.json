{"_from": "is-path-inside@^3.0.3", "_id": "is-path-inside@3.0.3", "_inBundle": false, "_integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==", "_location": "/is-path-inside", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-path-inside@^3.0.3", "name": "is-path-inside", "escapedName": "is-path-inside", "rawSpec": "^3.0.3", "saveSpec": null, "fetchSpec": "^3.0.3"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz", "_shasum": "d231362e53a07ff2b0e0ea7fed049161ffd16283", "_spec": "is-path-inside@^3.0.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-path-inside/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a path is inside another path", "devDependencies": {"ava": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-path-inside#readme", "keywords": ["path", "inside", "folder", "directory", "dir", "file", "resolve"], "license": "MIT", "name": "is-path-inside", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-path-inside.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.0.3"}