{"_from": "is-set@^2.0.3", "_id": "is-set@2.0.3", "_inBundle": false, "_integrity": "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==", "_location": "/is-set", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-set@^2.0.3", "name": "is-set", "escapedName": "is-set", "rawSpec": "^2.0.3", "saveSpec": null, "fetchSpec": "^2.0.3"}, "_requiredBy": ["/es-abstract", "/which-collection"], "_resolved": "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz", "_shasum": "8ab209ea424608141372ded6e0cb200ef1d9d01d", "_spec": "is-set@^2.0.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-set/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Is this value a JS Set? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "core-js": "^2.6.12", "es5-shim": "^4.6.7", "es6-shim": "^0.35.8", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-set#readme", "keywords": ["map", "set", "collection", "is", "robust"], "license": "MIT", "main": "index.js", "name": "is-set", "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-set.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only && npm run tests:shims && npm run tests:corejs", "tests-only": "nyc tape 'test/**/*.js'", "tests:corejs": "nyc tape --require=core-js 'test/**/*.js'", "tests:shims": "nyc tape --require=es5-shim --require=es5-shim 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "2.0.3"}