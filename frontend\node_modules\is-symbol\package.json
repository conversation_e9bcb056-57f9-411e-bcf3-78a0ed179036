{"_from": "is-symbol@^1.0.4", "_id": "is-symbol@1.1.1", "_inBundle": false, "_integrity": "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==", "_location": "/is-symbol", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-symbol@^1.0.4", "name": "is-symbol", "escapedName": "is-symbol", "rawSpec": "^1.0.4", "saveSpec": null, "fetchSpec": "^1.0.4"}, "_requiredBy": ["/es-to-primitive", "/which-boxed-primitive"], "_resolved": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz", "_shasum": "f47761279f532e2b05a7024a7506dbbedacd0634", "_spec": "is-symbol@^1.0.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-to-primitive", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-symbol/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "deprecated": false, "description": "Determine if a value is an ES6 Symbol or not.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-symbol#readme", "keywords": ["symbol", "es6", "is", "Symbol"], "license": "MIT", "main": "index.js", "name": "is-symbol", "publishConfig": {"ignore": [".github/workflows", ".nvmrc"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-symbol.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.1"}