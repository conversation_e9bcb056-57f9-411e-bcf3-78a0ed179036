{"_from": "is-typed-array@^1.1.15", "_id": "is-typed-array@1.1.15", "_inBundle": false, "_integrity": "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==", "_location": "/is-typed-array", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-typed-array@^1.1.15", "name": "is-typed-array", "escapedName": "is-typed-array", "rawSpec": "^1.1.15", "saveSpec": null, "fetchSpec": "^1.1.15"}, "_requiredBy": ["/es-abstract", "/is-data-view", "/typed-array-buffer", "/typed-array-byte-length", "/typed-array-byte-offset", "/typed-array-length"], "_resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "_shasum": "4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b", "_spec": "is-typed-array@^1.1.15", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "1.1.10"}, "bugs": {"url": "https://github.com/inspect-js/is-typed-array/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"which-typed-array": "^1.1.16"}, "deprecated": false, "description": "Is this value a JS Typed Array? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and despite ES6 Symbol.toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/is-callable": "^1.1.2", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/node": "^20.17.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-typed-array#readme", "keywords": ["array", "TypedArray", "typed array", "is", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "license": "MIT", "main": "index.js", "name": "is-typed-array", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/is-typed-array.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only && npm run test:harmony", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc tape test", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "types": "./index.d.ts", "version": "1.1.15"}