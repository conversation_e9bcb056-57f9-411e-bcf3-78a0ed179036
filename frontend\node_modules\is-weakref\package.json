{"_from": "is-weakref@^1.1.1", "_id": "is-weakref@1.1.1", "_inBundle": false, "_integrity": "sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==", "_location": "/is-weakref", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-weakref@^1.1.1", "name": "is-weakref", "escapedName": "is-weakref", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/es-abstract", "/which-builtin-type"], "_resolved": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz", "_shasum": "eea430182be8d64174bd96bffbc46f21bf3f9293", "_spec": "is-weakref@^1.1.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/is-weakref/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.3"}, "deprecated": false, "description": "Is this value a JS WeakRef? This module works cross-realm/iframe, and despite ES6 @@toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "for-each": "^0.3.4", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/is-weakref#readme", "keywords": ["weakref", "weak", "ref", "finalization", "finalization registry"], "license": "MIT", "main": "index.js", "name": "is-weakref", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-weakref.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.1.1"}