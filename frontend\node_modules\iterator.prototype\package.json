{"_from": "iterator.prototype@^1.1.4", "_id": "iterator.prototype@1.1.5", "_inBundle": false, "_integrity": "sha512-H0dkQoCa3b2VEeKQBOxFph+JAbcrQdE7KC0UkqwpLmv2EC4P41QXP+rqo9wYodACiG5/WM5s9oDApTU8utwj9g==", "_location": "/iterator.prototype", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "iterator.prototype@^1.1.4", "name": "iterator.prototype", "escapedName": "iterator.prototype", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/es-iterator-helpers"], "_resolved": "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.5.tgz", "_shasum": "12c959a29de32de0aa3bbbb801f4d777066dae39", "_spec": "iterator.prototype@^1.1.4", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-iterator-helpers", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/Iterator.prototype/issues"}, "bundleDependencies": false, "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "deprecated": false, "description": "`Iterator.prototype`, or a shared object to use.", "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/Iterator.prototype#readme", "keywords": ["javascript", "ecmascript", "iterator", "prototype"], "license": "MIT", "main": "index.js", "name": "iterator.prototype", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/Iterator.prototype.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.1.5"}