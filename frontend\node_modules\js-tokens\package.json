{"_from": "js-tokens@^3.0.0 || ^4.0.0", "_id": "js-tokens@4.0.0", "_inBundle": false, "_integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "_location": "/js-tokens", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "js-tokens@^3.0.0 || ^4.0.0", "name": "js-tokens", "escapedName": "js-tokens", "rawSpec": "^3.0.0 || ^4.0.0", "saveSpec": null, "fetchSpec": "^3.0.0 || ^4.0.0"}, "_requiredBy": ["/@babel/code-frame", "/loose-envify"], "_resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "_shasum": "19203fb59991df98e3a287050d4647cdeaf32499", "_spec": "js-tokens@^3.0.0 || ^4.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\loose-envify", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/lydell/js-tokens/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A regex that tokenizes JavaScript.", "devDependencies": {"coffeescript": "2.1.1", "esprima": "4.0.0", "everything.js": "1.0.3", "mocha": "5.0.0"}, "files": ["index.js"], "homepage": "https://github.com/lydell/js-tokens#readme", "keywords": ["JavaScript", "js", "token", "tokenize", "regex"], "license": "MIT", "name": "js-tokens", "repository": {"type": "git", "url": "git+https://github.com/lydell/js-tokens.git"}, "scripts": {"build": "node generate-index.js", "dev": "npm run build && npm test", "esprima-compare": "node esprima-compare ./index.js everything.js/es5.js", "test": "mocha --ui tdd"}, "version": "4.0.0"}