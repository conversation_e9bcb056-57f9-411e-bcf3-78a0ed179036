{"_from": "js-yaml@^4.1.0", "_id": "js-yaml@4.1.0", "_inBundle": false, "_integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "_location": "/js-yaml", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "js-yaml@^4.1.0", "name": "js-yaml", "escapedName": "js-yaml", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/@eslint/eslintrc", "/eslint"], "_resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "_shasum": "c1fb65f8f5017901cdd2c951864ba18458a10602", "_spec": "js-yaml@^4.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"js-yaml": "bin/js-yaml.js"}, "bugs": {"url": "https://github.com/nodeca/js-yaml/issues"}, "bundleDependencies": false, "contributors": [{"name": "Aleksey V Zapparov", "email": "<EMAIL>", "url": "http://www.ixti.net/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/puzrin"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://got-ravings.blogspot.com"}], "dependencies": {"argparse": "^2.0.1"}, "deprecated": false, "description": "YAML 1.2 parser and serializer", "devDependencies": {"@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "ansi": "^0.3.1", "benchmark": "^2.1.4", "codemirror": "^5.13.4", "eslint": "^7.0.0", "fast-check": "^2.8.0", "gh-pages": "^3.1.0", "mocha": "^8.2.1", "nyc": "^15.1.0", "rollup": "^2.34.1", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-terser": "^7.0.2", "shelljs": "^0.8.4"}, "exports": {".": {"import": "./dist/js-yaml.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "files": ["index.js", "lib/", "bin/", "dist/"], "homepage": "https://github.com/nodeca/js-yaml#readme", "jsdelivr": "dist/js-yaml.min.js", "keywords": ["yaml", "parser", "serializer", "pyyaml"], "license": "MIT", "module": "./dist/js-yaml.mjs", "name": "js-yaml", "repository": {"type": "git", "url": "git+https://github.com/nodeca/js-yaml.git"}, "scripts": {"browserify": "rollup -c support/rollup.config.js", "coverage": "npm run lint && nyc mocha && nyc report --reporter html", "demo": "npm run lint && node support/build_demo.js", "gh-demo": "npm run demo && gh-pages -d demo -f", "lint": "eslint .", "prepublishOnly": "npm run gh-demo", "test": "npm run lint && mocha"}, "unpkg": "dist/js-yaml.min.js", "version": "4.1.0"}