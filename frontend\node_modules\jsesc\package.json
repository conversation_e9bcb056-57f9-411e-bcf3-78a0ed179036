{"_from": "jsesc@^3.0.2", "_id": "jsesc@3.1.0", "_inBundle": false, "_integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "_location": "/jsesc", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "jsesc@^3.0.2", "name": "jsesc", "escapedName": "jsesc", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/@babel/generator"], "_resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "_shasum": "74d335a234f67ed19907fdadfac7ccf9d409825d", "_spec": "jsesc@^3.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\generator", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bin": {"jsesc": "bin/jsesc"}, "bugs": {"url": "https://github.com/mathiasbynens/jsesc/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Given some data, jsesc returns the shortest possible stringified & ASCII-safe representation of that data.", "devDependencies": {"coveralls": "^2.11.6", "grunt": "^0.4.5", "grunt-cli": "^1.3.2", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "mocha": "^5.2.0", "regenerate": "^1.3.0", "requirejs": "^2.1.22", "unicode-13.0.0": "0.8.0"}, "engines": {"node": ">=6"}, "files": ["LICENSE-MIT.txt", "jsesc.js", "bin/", "man/"], "homepage": "https://mths.be/jsesc", "keywords": ["buffer", "escape", "javascript", "json", "map", "set", "string", "stringify", "tool"], "license": "MIT", "main": "jsesc.js", "man": ["man/jsesc.1"], "name": "jsesc", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/jsesc.git"}, "scripts": {"build": "grunt template", "cover": "istanbul cover --report 'html' --verbose --dir 'coverage' 'tests/tests.js'", "coveralls": "istanbul cover --verbose --dir 'coverage' 'tests/tests.js' && coveralls < coverage/lcov.info'", "test": "mocha tests"}, "version": "3.1.0"}