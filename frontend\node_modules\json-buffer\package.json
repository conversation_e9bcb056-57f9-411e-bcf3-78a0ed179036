{"_from": "json-buffer@3.0.1", "_id": "json-buffer@3.0.1", "_inBundle": false, "_integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "_location": "/json-buffer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "json-buffer@3.0.1", "name": "json-buffer", "escapedName": "json-buffer", "rawSpec": "3.0.1", "saveSpec": null, "fetchSpec": "3.0.1"}, "_requiredBy": ["/keyv"], "_resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "_shasum": "9338802a30d3b6605fbe0613e094008ca8c05a13", "_spec": "json-buffer@3.0.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\keyv", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://dominictarr.com"}, "bugs": {"url": "https://github.com/dominictarr/json-buffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JSON parse & stringify that supports binary via bops & base64", "devDependencies": {"tape": "^4.6.3"}, "homepage": "https://github.com/dominictarr/json-buffer", "license": "MIT", "name": "json-buffer", "repository": {"type": "git", "url": "git://github.com/dominictarr/json-buffer.git"}, "scripts": {"test": "set -e; for t in test/*.js; do node $t; done"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "3.0.1"}