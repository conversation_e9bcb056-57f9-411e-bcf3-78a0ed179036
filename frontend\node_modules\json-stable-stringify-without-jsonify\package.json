{"_from": "json-stable-stringify-without-jsonify@^1.0.1", "_id": "json-stable-stringify-without-jsonify@1.0.1", "_inBundle": false, "_integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "_location": "/json-stable-stringify-without-jsonify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "json-stable-stringify-without-jsonify@^1.0.1", "name": "json-stable-stringify-without-jsonify", "escapedName": "json-stable-stringify-without-jsonify", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "_shasum": "9db7b59496ad3f3cfef30a75142d2d930ad72651", "_spec": "json-stable-stringify-without-jsonify@^1.0.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/samn/json-stable-stringify/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "deterministic JSON.stringify() with custom sorting to get deterministic hashes from stringified results, with no public domain dependencies", "devDependencies": {"tape": "~1.0.4"}, "homepage": "https://github.com/samn/json-stable-stringify", "keywords": ["json", "stringify", "deterministic", "hash", "sort", "stable"], "license": "MIT", "main": "index.js", "name": "json-stable-stringify-without-jsonify", "repository": {"type": "git", "url": "git://github.com/samn/json-stable-stringify.git"}, "scripts": {"test": "tape test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "ff/5", "ff/latest", "chrome/15", "chrome/latest", "safari/latest", "opera/latest"]}, "version": "1.0.1"}