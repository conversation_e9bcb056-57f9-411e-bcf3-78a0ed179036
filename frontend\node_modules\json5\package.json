{"_from": "json5@^2.2.3", "_id": "json5@2.2.3", "_inBundle": false, "_integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "_location": "/json5", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "json5@^2.2.3", "name": "json5", "escapedName": "json5", "rawSpec": "^2.2.3", "saveSpec": null, "fetchSpec": "^2.2.3"}, "_requiredBy": ["/@babel/core"], "_resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "_shasum": "78cd6f1a19bdc12b73db5ad0c61efd66c1e29283", "_spec": "json5@^2.2.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@babel\\core", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"json5": "lib/cli.js"}, "browser": "dist/index.js", "bugs": {"url": "https://github.com/json5/json5/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "jordan<PERSON><EMAIL>"}], "deprecated": false, "description": "JSON for Humans", "devDependencies": {"core-js": "^2.6.5", "eslint": "^5.15.3", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "npm-run-all": "^4.1.5", "regenerate": "^1.4.0", "rollup": "^0.64.1", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.2.1", "rollup-plugin-node-resolve": "^3.4.0", "rollup-plugin-terser": "^1.0.1", "sinon": "^6.3.5", "tap": "^12.6.0", "unicode-10.0.0": "^0.7.5"}, "engines": {"node": ">=6"}, "files": ["lib/", "dist/"], "homepage": "http://json5.org/", "keywords": ["json", "json5", "es5", "es2015", "ecmascript"], "license": "MIT", "main": "lib/index.js", "module": "dist/index.mjs", "name": "json5", "repository": {"type": "git", "url": "git+https://github.com/json5/json5.git"}, "scripts": {"build": "rollup -c", "build-package": "node build/package.js", "build-unicode": "node build/unicode.js", "coverage": "tap --coverage-report html test", "lint": "eslint --fix .", "lint-report": "eslint .", "prepublishOnly": "npm run production", "preversion": "npm run production", "production": "run-s test build", "tap": "tap -Rspec --100 test", "test": "run-s lint-report tap", "version": "npm run build-package && git add package.json5"}, "types": "lib/index.d.ts", "version": "2.2.3"}