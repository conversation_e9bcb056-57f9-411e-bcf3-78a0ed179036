{"_from": "keyv@^4.5.3", "_id": "keyv@4.5.4", "_inBundle": false, "_integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "_location": "/keyv", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "keyv@^4.5.3", "name": "keyv", "escapedName": "keyv", "rawSpec": "^4.5.3", "saveSpec": null, "fetchSpec": "^4.5.3"}, "_requiredBy": ["/flat-cache"], "_resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "_shasum": "a879a99e29452f942439f2a405e3af8b31d4de93", "_spec": "keyv@^4.5.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\flat-cache", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jaredwray.com"}, "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "bundleDependencies": false, "dependencies": {"json-buffer": "3.0.1"}, "deprecated": false, "description": "Simple key-value storage with support for multiple backends", "devDependencies": {"@keyv/test-suite": "*", "eslint": "^8.51.0", "eslint-plugin-promise": "^6.1.1", "pify": "^5.0.0", "timekeeper": "^2.3.1", "tsd": "^0.29.0"}, "files": ["src"], "homepage": "https://github.com/jaredwray/keyv", "keywords": ["key", "value", "store", "cache", "ttl"], "license": "MIT", "main": "src/index.js", "name": "keyv", "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "scripts": {"build": "echo 'No build step required.'", "clean": "rm -rf node_modules && rm -rf ./coverage && rm -rf ./test/testdb.sqlite", "prepare": "yarn build", "test": "xo && c8 ava --serial", "test:ci": "xo && ava --serial"}, "tsd": {"directory": "test"}, "types": "./src/index.d.ts", "version": "4.5.4", "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-node-protocol": 0, "@typescript-eslint/consistent-type-definitions": 0, "unicorn/no-typeof-undefined": 0, "unicorn/prefer-event-target": 0}}}