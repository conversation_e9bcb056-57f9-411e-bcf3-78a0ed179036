{"_from": "levn@^0.4.1", "_id": "levn@0.4.1", "_inBundle": false, "_integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "_location": "/levn", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "levn@^0.4.1", "name": "levn", "escapedName": "levn", "rawSpec": "^0.4.1", "saveSpec": null, "fetchSpec": "^0.4.1"}, "_requiredBy": ["/eslint", "/optionator"], "_resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "_shasum": "ae4562c007473b932a6200d403268dd2fffc6ade", "_spec": "levn@^0.4.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/gkz/levn/issues"}, "bundleDependencies": false, "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "deprecated": false, "description": "Light ECMAScript (JavaScript) Value Notation - human written, concise, typed, flexible", "devDependencies": {"livescript": "^1.6.0", "mocha": "^7.1.1"}, "engines": {"node": ">= 0.8.0"}, "files": ["lib", "README.md", "LICENSE"], "homepage": "https://github.com/gkz/levn", "keywords": ["levn", "light", "ecmascript", "value", "notation", "json", "typed", "human", "concise", "typed", "flexible"], "license": "MIT", "main": "./lib/", "name": "levn", "repository": {"type": "git", "url": "git://github.com/gkz/levn.git"}, "scripts": {"test": "make test"}, "version": "0.4.1"}