{"_from": "lodash.merge@^4.6.2", "_id": "lodash.merge@4.6.2", "_inBundle": false, "_integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "_location": "/lodash.merge", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "lodash.merge@^4.6.2", "name": "lodash.merge", "escapedName": "lodash.merge", "rawSpec": "^4.6.2", "saveSpec": null, "fetchSpec": "^4.6.2"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "_shasum": "558aa53b43b661e1925a0afdfa36a9a1085fe57a", "_spec": "lodash.merge@^4.6.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/lodash/lodash/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "The Lodash method `_.merge` exported as a module.", "homepage": "https://lodash.com/", "icon": "https://lodash.com/icon.svg", "keywords": ["lodash-modularized", "merge"], "license": "MIT", "name": "lodash.merge", "repository": {"type": "git", "url": "git+https://github.com/lodash/lodash.git"}, "scripts": {"test": "echo \"See https://travis-ci.org/lodash/lodash-cli for testing details.\""}, "version": "4.6.2"}