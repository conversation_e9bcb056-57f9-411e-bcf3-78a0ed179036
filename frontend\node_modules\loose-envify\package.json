{"_from": "loose-envify@^1.1.0", "_id": "loose-envify@1.4.0", "_inBundle": false, "_integrity": "sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==", "_location": "/loose-envify", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "loose-envify@^1.1.0", "name": "loose-envify", "escapedName": "loose-envify", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/prop-types", "/react", "/react-dom", "/scheduler"], "_resolved": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "_shasum": "71ee51fa7be4caec1a63839f7e682d8132d30caf", "_spec": "loose-envify@^1.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\react", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"loose-envify": "cli.js"}, "bugs": {"url": "https://github.com/zertosh/loose-envify/issues"}, "bundleDependencies": false, "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "deprecated": false, "description": "Fast (and loose) selective `process.env` replacer using js-tokens instead of an AST", "devDependencies": {"browserify": "^13.1.1", "envify": "^3.4.0", "tap": "^8.0.0"}, "homepage": "https://github.com/zertosh/loose-envify", "keywords": ["environment", "variables", "browserify", "browserify-transform", "transform", "source", "configuration"], "license": "MIT", "main": "index.js", "name": "loose-envify", "repository": {"type": "git", "url": "git://github.com/zertosh/loose-envify.git"}, "scripts": {"test": "tap test/*.js"}, "version": "1.4.0"}