{"_from": "natural-compare@^1.4.0", "_id": "natural-compare@1.4.0", "_inBundle": false, "_integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "_location": "/natural-compare", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "natural-compare@^1.4.0", "name": "natural-compare", "escapedName": "natural-compare", "rawSpec": "^1.4.0", "saveSpec": null, "fetchSpec": "^1.4.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "_shasum": "4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7", "_spec": "natural-compare@^1.4.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/litejs/natural-compare-lite"}, "bugs": {"url": "https://github.com/litejs/natural-compare-lite/issues"}, "buildman": {"dist/index-min.js": {"banner": "/*! litejs.com/MIT-LICENSE.txt */", "input": "index.js"}}, "bundleDependencies": false, "deprecated": false, "description": "Compare strings containing a mix of letters and numbers in the way a human being would in sort order.", "devDependencies": {"buildman": "*", "testman": "*"}, "files": ["index.js"], "homepage": "https://github.com/litejs/natural-compare-lite#readme", "keywords": ["string", "natural", "order", "sort", "natsort", "natcmp", "compare", "alphanum", "litejs"], "license": "MIT", "main": "index.js", "name": "natural-compare", "repository": {"type": "git", "url": "git://github.com/litejs/natural-compare-lite.git"}, "scripts": {"build": "node node_modules/buildman/index.js --all", "test": "node tests/index.js"}, "stability": 3, "version": "1.4.0"}