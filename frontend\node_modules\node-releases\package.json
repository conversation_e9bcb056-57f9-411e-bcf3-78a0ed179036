{"_from": "node-releases@^2.0.21", "_id": "node-releases@2.0.21", "_inBundle": false, "_integrity": "sha512-5b0pgg78U3hwXkCM8Z9b2FJdPZlr9Psr9V2gQPESdGHqbntyFJKFW4r5TeWGFzafGY3hzs1JC62VEQMbl1JFkw==", "_location": "/node-releases", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "node-releases@^2.0.21", "name": "node-releases", "escapedName": "node-releases", "rawSpec": "^2.0.21", "saveSpec": null, "fetchSpec": "^2.0.21"}, "_requiredBy": ["/browserslist"], "_resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.21.tgz", "_shasum": "f59b018bc0048044be2d4c4c04e4c8b18160894c", "_spec": "node-releases@^2.0.21", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\browserslist", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/chicoxyzzy/node-releases/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Node.js releases data", "devDependencies": {"semver": "^7.3.5"}, "homepage": "https://github.com/chicoxyzzy/node-releases#readme", "keywords": ["nodejs", "releases"], "license": "MIT", "name": "node-releases", "repository": {"type": "git", "url": "git+https://github.com/chicoxyzzy/node-releases.git"}, "scripts": {"build": "node scripts/build.js"}, "type": "module", "version": "2.0.21"}