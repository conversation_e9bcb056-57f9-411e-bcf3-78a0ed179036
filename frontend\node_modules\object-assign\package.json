{"_from": "object-assign@^4.1.1", "_id": "object-assign@4.1.1", "_inBundle": false, "_integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "_location": "/object-assign", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object-assign@^4.1.1", "name": "object-assign", "escapedName": "object-assign", "rawSpec": "^4.1.1", "saveSpec": null, "fetchSpec": "^4.1.1"}, "_requiredBy": ["/prop-types"], "_resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "_shasum": "2109adc7965887cfc05cbbd442cac8bfbb360863", "_spec": "object-assign@^4.1.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\prop-types", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/object-assign/issues"}, "bundleDependencies": false, "deprecated": false, "description": "ES2015 `Object.assign()` ponyfill", "devDependencies": {"ava": "^0.16.0", "lodash": "^4.16.4", "matcha": "^0.7.0", "xo": "^0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/object-assign#readme", "keywords": ["object", "assign", "extend", "properties", "es2015", "ecmascript", "harmony", "ponyfill", "prollyfill", "polyfill", "shim", "browser"], "license": "MIT", "name": "object-assign", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/object-assign.git"}, "scripts": {"bench": "matcha bench.js", "test": "xo && ava"}, "version": "4.1.1"}