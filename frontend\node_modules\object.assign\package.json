{"_from": "object.assign@^4.1.7", "_id": "object.assign@4.1.7", "_inBundle": false, "_integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==", "_location": "/object.assign", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object.assign@^4.1.7", "name": "object.assign", "escapedName": "object.assign", "rawSpec": "^4.1.7", "saveSpec": null, "fetchSpec": "^4.1.7"}, "_requiredBy": ["/es-abstract", "/jsx-ast-utils"], "_resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "_shasum": "8c14ca1a424c6a561b0bb2a22f66f5049a945d3d", "_spec": "object.assign@^4.1.7", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ljharb/object.assign/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "deprecated": false, "description": "ES6 spec-compliant Object.assign shim. From https://github.com/es-shims/es6-shim", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "browserify": "^16.5.2", "eslint": "=8.8.0", "for-each": "^0.3.3", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "hasown": "^2.0.2", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "ses": "^1.10.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/object.assign#readme", "keywords": ["Object.assign", "assign", "ES6", "extend", "$.extend", "j<PERSON><PERSON><PERSON>", "_.extend", "Underscore", "es-shim API", "polyfill", "shim"], "license": "MIT", "main": "index.js", "name": "object.assign", "publishConfig": {"ignore": [".github/workflows", "bower.json", "browserShim.js", "!dist/"]}, "repository": {"type": "git", "url": "git://github.com/ljharb/object.assign.git"}, "scripts": {"build": "mkdir -p dist && browserify browserShim.js > dist/browser.js", "lint": "eslint .", "posttest": "npx npm@'>=10.2' audit --production", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run build", "pretest": "npm run lint && es-shim-api --bound", "test": "npm run tests-only && npm run test:ses", "test:implementation": "nyc node test", "test:native": "nyc node test/native", "test:ses": "node test/ses-compat", "test:shim": "nyc node test/shimmed", "tests-only": "npm run test:implementation && npm run test:shim"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "4.1.7"}