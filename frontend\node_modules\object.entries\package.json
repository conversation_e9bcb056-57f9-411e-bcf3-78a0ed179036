{"_from": "object.entries@^1.1.9", "_id": "object.entries@1.1.9", "_inBundle": false, "_integrity": "sha512-8u/hfXFRBD1O0hPUjioLhoWFHRmt6tKA4/vZPyckBr18l1KE9uHrFaFaUi8MDRTpi4uak2goyPTSNJLXX2k2Hw==", "_location": "/object.entries", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "object.entries@^1.1.9", "name": "object.entries", "escapedName": "object.entries", "rawSpec": "^1.1.9", "saveSpec": null, "fetchSpec": "^1.1.9"}, "_requiredBy": ["/eslint-plugin-react"], "_resolved": "https://registry.npmjs.org/object.entries/-/object.entries-1.1.9.tgz", "_shasum": "e4770a6a1444afb61bd39f984018b5bede25f8b3", "_spec": "object.entries@^1.1.9", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint-plugin-react", "author": {"name": "<PERSON>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/Object.entries/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "deprecated": false, "description": "ES2017 spec-compliant Object.entries shim.", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "array.prototype.map": "^1.0.8", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/es-shims/Object.entries#readme", "keywords": ["Object.entries", "Object.values", "Object.keys", "entries", "values", "ES7", "ES8", "ES2017", "shim", "object", "keys", "polyfill", "es-shim API"], "license": "MIT", "main": "index.js", "name": "object.entries", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/es-shims/Object.entries.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "posttest": "npx npm@\">= 10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js", "browsers": ["iexplore/9.0..latest", "firefox/4.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/11.6..latest", "opera/next", "safari/5.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "version": "1.1.9"}