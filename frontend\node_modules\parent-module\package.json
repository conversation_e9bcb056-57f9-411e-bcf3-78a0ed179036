{"_from": "parent-module@^1.0.0", "_id": "parent-module@1.0.1", "_inBundle": false, "_integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "_location": "/parent-module", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parent-module@^1.0.0", "name": "parent-module", "escapedName": "parent-module", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/import-fresh"], "_resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "_shasum": "691d2709e78c79fae3a156622452d00762caaaa2", "_spec": "parent-module@^1.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\import-fresh", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/parent-module/issues"}, "bundleDependencies": false, "dependencies": {"callsites": "^3.0.0"}, "deprecated": false, "description": "Get the path of the parent module", "devDependencies": {"ava": "^1.4.1", "execa": "^1.0.0", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/parent-module#readme", "keywords": ["parent", "module", "package", "pkg", "caller", "calling", "module", "path", "callsites", "callsite", "stacktrace", "stack", "trace", "function", "file"], "license": "MIT", "name": "parent-module", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/parent-module.git"}, "scripts": {"test": "xo && ava"}, "version": "1.0.1"}