{"_from": "path-parse@^1.0.7", "_id": "path-parse@1.0.7", "_inBundle": false, "_integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "_location": "/path-parse", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-parse@^1.0.7", "name": "path-parse", "escapedName": "path-parse", "rawSpec": "^1.0.7", "saveSpec": null, "fetchSpec": "^1.0.7"}, "_requiredBy": ["/resolve"], "_resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "_shasum": "fbc114b60ca42b30d9daf5858e4bd68bbedb6735", "_spec": "path-parse@^1.0.7", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\resolve", "author": {"name": "<PERSON>", "email": "http://jbgutierrez.info"}, "bugs": {"url": "https://github.com/jbgutierrez/path-parse/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Node.js path.parse() ponyfill", "homepage": "https://github.com/jbgutierrez/path-parse#readme", "keywords": ["path", "paths", "file", "dir", "parse", "built-in", "util", "utils", "core", "ponyfill", "polyfill", "shim"], "license": "MIT", "main": "index.js", "name": "path-parse", "repository": {"type": "git", "url": "git+https://github.com/jbgutierrez/path-parse.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.7"}