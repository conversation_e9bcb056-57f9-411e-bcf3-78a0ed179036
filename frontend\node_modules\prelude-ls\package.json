{"_from": "prelude-ls@^1.2.1", "_id": "prelude-ls@1.2.1", "_inBundle": false, "_integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "_location": "/prelude-ls", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "prelude-ls@^1.2.1", "name": "prelude-ls", "escapedName": "prelude-ls", "rawSpec": "^1.2.1", "saveSpec": null, "fetchSpec": "^1.2.1"}, "_requiredBy": ["/levn", "/optionator", "/type-check"], "_resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "_shasum": "debc6489d7a6e6b0e7611888cec880337d316396", "_spec": "prelude-ls@^1.2.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\levn", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/gkz/prelude-ls/issues"}, "bundleDependencies": false, "deprecated": false, "description": "prelude.ls is a functionally oriented utility library. It is powerful and flexible. Almost all of its functions are curried. It is written in, and is the recommended base library for, LiveScript.", "devDependencies": {"browserify": "^16.5.1", "livescript": "^1.6.0", "mocha": "^7.1.1", "sinon": "~8.0.1", "uglify-js": "^3.8.1"}, "engines": {"node": ">= 0.8.0"}, "files": ["lib/", "README.md", "LICENSE"], "homepage": "http://preludels.com", "keywords": ["prelude", "livescript", "utility", "ls", "coffeescript", "javascript", "library", "functional", "array", "list", "object", "string"], "license": "MIT", "main": "lib/", "name": "prelude-ls", "repository": {"type": "git", "url": "git://github.com/gkz/prelude-ls.git"}, "scripts": {"test": "make test"}, "version": "1.2.1"}