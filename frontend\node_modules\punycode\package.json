{"_from": "punycode@^2.1.0", "_id": "punycode@2.3.1", "_inBundle": false, "_integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "_location": "/punycode", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "punycode@^2.1.0", "name": "punycode", "escapedName": "punycode", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/uri-js"], "_resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "_shasum": "027422e2faec0b25e1549c3e1bd8309b9133b6e5", "_spec": "punycode@^2.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\uri-js", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/punycode.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://mathiasbynens.be/"}], "deprecated": false, "description": "A robust Punycode converter that fully complies to RFC 3492 and RFC 5891, and works on nearly all JavaScript platforms.", "devDependencies": {"codecov": "^3.8.3", "mocha": "^10.2.0", "nyc": "^15.1.0"}, "engines": {"node": ">=6"}, "files": ["LICENSE-MIT.txt", "punycode.js", "punycode.es6.js"], "homepage": "https://mths.be/punycode", "jsnext:main": "punycode.es6.js", "jspm": {"map": {"./punycode.js": {"node": "@node/punycode"}}}, "keywords": ["punycode", "unicode", "idn", "idna", "dns", "url", "domain"], "license": "MIT", "main": "punycode.js", "module": "punycode.es6.js", "name": "punycode", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/punycode.js.git"}, "scripts": {"build": "node scripts/prepublish.js", "test": "mocha tests"}, "version": "2.3.1"}