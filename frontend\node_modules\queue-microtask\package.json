{"_from": "queue-microtask@^1.2.2", "_id": "queue-microtask@1.2.3", "_inBundle": false, "_integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "_location": "/queue-microtask", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "queue-microtask@^1.2.2", "name": "queue-microtask", "escapedName": "queue-microtask", "rawSpec": "^1.2.2", "saveSpec": null, "fetchSpec": "^1.2.2"}, "_requiredBy": ["/run-parallel"], "_resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "_shasum": "4929228bbc724dfac43e0efb058caf7b6cfb6243", "_spec": "queue-microtask@^1.2.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\run-parallel", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/feross/queue-microtask/issues"}, "bundleDependencies": false, "deprecated": false, "description": "fast, tiny `queueMicrotask` shim for modern engines", "devDependencies": {"standard": "*", "tape": "^5.2.2"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "homepage": "https://github.com/feross/queue-microtask", "keywords": ["asap", "immediate", "micro task", "microtask", "nextTick", "process.nextTick", "queue micro task", "queue microtask", "queue-microtask", "queueMicrotask", "setImmediate", "task"], "license": "MIT", "main": "index.js", "name": "queue-microtask", "repository": {"type": "git", "url": "git://github.com/feross/queue-microtask.git"}, "scripts": {"test": "standard && tape test/*.js"}, "version": "1.2.3"}