{"_from": "react-is@^16.13.1", "_id": "react-is@16.13.1", "_inBundle": false, "_integrity": "sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==", "_location": "/react-is", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-is@^16.13.1", "name": "react-is", "escapedName": "react-is", "rawSpec": "^16.13.1", "saveSpec": null, "fetchSpec": "^16.13.1"}, "_requiredBy": ["/prop-types"], "_resolved": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "_shasum": "789729a4dc36de2999dc156dd6c1d9c18cea56a4", "_spec": "react-is@^16.13.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\prop-types", "bugs": {"url": "https://github.com/facebook/react/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Brand checking of React Elements.", "files": ["LICENSE", "README.md", "build-info.json", "index.js", "cjs/", "umd/"], "homepage": "https://reactjs.org/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "react-is", "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "packages/react-is"}, "version": "16.13.1"}