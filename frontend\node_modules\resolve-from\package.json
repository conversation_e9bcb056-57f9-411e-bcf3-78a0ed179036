{"_from": "resolve-from@^4.0.0", "_id": "resolve-from@4.0.0", "_inBundle": false, "_integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "_location": "/resolve-from", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve-from@^4.0.0", "name": "resolve-from", "escapedName": "resolve-from", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/import-fresh"], "_resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "_shasum": "4abcd852ad32dd7baabfe9b40e00a36db5f392e6", "_spec": "resolve-from@^4.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\import-fresh", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/resolve-from/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Resolve the path of a module like `require.resolve()` but from a given path", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/resolve-from#readme", "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "license": "MIT", "name": "resolve-from", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/resolve-from.git"}, "scripts": {"test": "xo && ava"}, "version": "4.0.0"}