{"_from": "resolve@^2.0.0-next.5", "_id": "resolve@2.0.0-next.5", "_inBundle": false, "_integrity": "sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==", "_location": "/resolve", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resolve@^2.0.0-next.5", "name": "resolve", "escapedName": "resolve", "rawSpec": "^2.0.0-next.5", "saveSpec": null, "fetchSpec": "^2.0.0-next.5"}, "_requiredBy": ["/eslint-plugin-react"], "_resolved": "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz", "_shasum": "6b0ec3107e671e52b68cd068ef327173b90dc03c", "_spec": "resolve@^2.0.0-next.5", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint-plugin-react", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bin": {"resolve": "bin/resolve"}, "bugs": {"url": "https://github.com/browserify/resolve/issues"}, "bundleDependencies": false, "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "deprecated": false, "description": "resolve like require.resolve() on behalf of files asynchronously and synchronously", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "array.prototype.map": "^1.0.6", "aud": "^2.0.3", "copy-dir": "^1.3.0", "eclint": "^2.8.1", "eslint": "=8.8.0", "in-publish": "^2.0.1", "mkdirp": "^0.5.5", "mv": "^2.1.1", "npmignore": "^0.3.0", "object-keys": "^1.1.1", "rimraf": "^2.7.1", "safe-publish-latest": "^2.0.0", "tap": "^0.4.13", "tape": "^5.7.0", "tmp": "^0.0.31"}, "exports": {".": [{"import": "./index.mjs", "default": "./index.js"}, "./index.js"], "./sync": "./lib/sync.js", "./async": "./lib/async.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/browserify/resolve#readme", "keywords": ["resolve", "require", "node", "module"], "license": "MIT", "main": "index.js", "name": "resolve", "publishConfig": {"ignore": [".github/workflows", "appveyor.yml", "test/resolver/malformed_package_json"]}, "repository": {"type": "git", "url": "git://github.com/browserify/resolve.git"}, "scripts": {"lint": "eslint --ext=js,mjs --no-eslintrc -c .eslintrc . 'bin/**'", "posttest": "npm run test:multirepo && aud --production", "prelint": "eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "pretests-only": "cd ./test/resolver/nested_symlinks && node mylib/sync && node mylib/async", "test": "npm run --silent tests-only", "test:multirepo": "cd ./test/resolver/multirepo && npm install && npm test", "tests-only": "tape test/*.js"}, "version": "2.0.0-next.5"}