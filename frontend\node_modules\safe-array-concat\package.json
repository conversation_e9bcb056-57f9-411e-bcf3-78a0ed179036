{"_from": "safe-array-concat@^1.1.3", "_id": "safe-array-concat@1.1.3", "_inBundle": false, "_integrity": "sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==", "_location": "/safe-array-concat", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "safe-array-concat@^1.1.3", "name": "safe-array-concat", "escapedName": "safe-array-concat", "rawSpec": "^1.1.3", "saveSpec": null, "fetchSpec": "^1.1.3"}, "_requiredBy": ["/es-abstract", "/es-iterator-helpers"], "_resolved": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "_shasum": "c9e54ec4f603b0bbb8e7e5007a5ee7aecd1538c3", "_spec": "safe-array-concat@^1.1.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/safe-array-concat/issues"}, "bundleDependencies": false, "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "deprecated": false, "description": "`Array.prototype.concat`, but made safe by ignoring Symbol.isConcatSpreadable", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/get-intrinsic": "^1.2.3", "@types/has-symbols": "^1.0.2", "@types/isarray": "^2.0.2", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "mock-property": "^1.1.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "set-function-length": "^1.2.2", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">=0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/safe-array-concat#readme", "keywords": ["safe", "Array", "concat", "push", "isConcatSpreadable"], "license": "MIT", "main": "index.js", "name": "safe-array-concat", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/safe-array-concat.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape test", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.1.3"}