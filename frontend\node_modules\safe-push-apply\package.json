{"_from": "safe-push-apply@^1.0.0", "_id": "safe-push-apply@1.0.0", "_inBundle": false, "_integrity": "sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==", "_location": "/safe-push-apply", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "safe-push-apply@^1.0.0", "name": "safe-push-apply", "escapedName": "safe-push-apply", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/es-abstract", "/own-keys"], "_resolved": "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz", "_shasum": "01850e981c1602d398c85081f360e4e6d03d27f5", "_spec": "safe-push-apply@^1.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/safe-push-apply/issues"}, "bundleDependencies": false, "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "deprecated": false, "description": "Push an array of items into an array, while being robust against prototype modification", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/isarray": "^2.0.3", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/safe-push-apply#readme", "keywords": ["array", "push", "apply", "pushApply", "safe"], "license": "MIT", "main": "index.js", "name": "safe-push-apply", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/safe-push-apply.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape test", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.0.0"}