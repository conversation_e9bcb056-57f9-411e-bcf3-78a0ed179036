{"_from": "scheduler@^0.23.2", "_id": "scheduler@0.23.2", "_inBundle": false, "_integrity": "sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==", "_location": "/scheduler", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "scheduler@^0.23.2", "name": "scheduler", "escapedName": "scheduler", "rawSpec": "^0.23.2", "saveSpec": null, "fetchSpec": "^0.23.2"}, "_requiredBy": ["/react-dom"], "_resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.23.2.tgz", "_shasum": "414ba64a3b282892e944cf2108ecc078d115cdc3", "_spec": "scheduler@^0.23.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\react-dom", "browserify": {"transform": ["loose-envify"]}, "bugs": {"url": "https://github.com/facebook/react/issues"}, "bundleDependencies": false, "dependencies": {"loose-envify": "^1.1.0"}, "deprecated": false, "description": "Cooperative scheduler for the browser environment.", "files": ["LICENSE", "README.md", "index.js", "unstable_mock.js", "unstable_post_task.js", "cjs/", "umd/"], "homepage": "https://reactjs.org/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "scheduler", "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "packages/scheduler"}, "version": "0.23.2"}