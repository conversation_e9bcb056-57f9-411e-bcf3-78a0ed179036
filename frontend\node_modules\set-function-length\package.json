{"_from": "set-function-length@^1.2.2", "_id": "set-function-length@1.2.2", "_inBundle": false, "_integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "_location": "/set-function-length", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "set-function-length@^1.2.2", "name": "set-function-length", "escapedName": "set-function-length", "rawSpec": "^1.2.2", "saveSpec": null, "fetchSpec": "^1.2.2"}, "_requiredBy": ["/call-bind"], "_resolved": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "_shasum": "aac72314198eaed975cf77b2c3b6b880695e5449", "_spec": "set-function-length@^1.2.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\call-bind", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/set-function-length/issues"}, "bundleDependencies": false, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "deprecated": false, "description": "Set a function's length property", "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@ljharb/eslint-config": "^21.1.0", "@ljharb/tsconfig": "^0.1.1", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/function-bind": "^1.1.10", "@types/gopd": "^1.0.3", "@types/has-property-descriptors": "^1.0.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "call-bind": "^1.0.7", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "directories": {"test": "test"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./env": "./env.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/set-function-length#readme", "keywords": ["javascript", "ecmascript", "set", "function", "length", "function.length"], "license": "MIT", "main": "index.js", "name": "set-function-length", "publishConfig": {"ignore": [".github/workflows", "test"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-length.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "npm run tsc", "posttest": "aud --production", "posttsc": "attw -P", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "tsc": "tsc -p .", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.2.2"}