{"_from": "set-function-name@^2.0.2", "_id": "set-function-name@2.0.2", "_inBundle": false, "_integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==", "_location": "/set-function-name", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "set-function-name@^2.0.2", "name": "set-function-name", "escapedName": "set-function-name", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/iterator.prototype", "/regexp.prototype.flags", "/string.prototype.matchall"], "_resolved": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz", "_shasum": "16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985", "_spec": "set-function-name@^2.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\regexp.prototype.flags", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/set-function-name/issues"}, "bundleDependencies": false, "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "deprecated": false, "description": "Set a function's name property", "devDependencies": {"@ljharb/eslint-config": "^21.1.0", "@types/call-bind": "^1.0.5", "@types/define-properties": "^1.1.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/function.prototype.name": "^1.1.3", "@types/functions-have-names": "^1.2.2", "@types/has-property-descriptors": "^1.0.3", "@types/make-arrow-function": "^1.2.2", "@types/make-async-function": "^1.0.2", "@types/make-async-generator-function": "^1.0.3", "@types/make-generator-function": "^2.0.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "es-value-fixtures": "^1.4.2", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "function.prototype.name": "^1.1.6", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-async-function": "^1.0.0", "make-async-generator-function": "^1.0.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "directories": {"test": "test"}, "engines": {"node": ">= 0.4"}, "homepage": "https://github.com/ljharb/set-function-name#readme", "keywords": ["set", "assign", "function", "name", "function.name"], "license": "MIT", "main": "index.js", "name": "set-function-name", "publishConfig": {"ignore": [".github/workflows", "test", "!*.d.ts", "!*.d.ts.map"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-function-name.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "posttest": "aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "types": "./index.d.ts", "version": "2.0.2"}