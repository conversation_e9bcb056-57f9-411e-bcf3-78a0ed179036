{"_from": "set-proto@^1.0.0", "_id": "set-proto@1.0.0", "_inBundle": false, "_integrity": "sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==", "_location": "/set-proto", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "set-proto@^1.0.0", "name": "set-proto", "escapedName": "set-proto", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz", "_shasum": "0760dbcff30b2d7e801fd6e19983e56da337565e", "_spec": "set-proto@^1.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/set-proto/issues"}, "bundleDependencies": false, "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "deprecated": false, "description": "<PERSON><PERSON><PERSON> set the [[Prototype]] of an object", "devDependencies": {"@arethetypeswrong/cli": "^0.17.2", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./Reflect.setPrototypeOf": "./Reflect.setPrototypeOf.js", "./Object.setPrototypeOf": "./Object.setPrototypeOf.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/set-proto#readme", "keywords": ["set", "proto", "prototype", "setPrototypeOf", "[[Prototype]]"], "license": "MIT", "main": "index.js", "name": "set-proto", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/set-proto.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@\">=10.2\" audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.0"}