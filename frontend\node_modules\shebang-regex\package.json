{"_from": "shebang-regex@^3.0.0", "_id": "shebang-regex@3.0.0", "_inBundle": false, "_integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "_location": "/shebang-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "shebang-regex@^3.0.0", "name": "shebang-regex", "escapedName": "shebang-regex", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/shebang-command"], "_resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "_shasum": "ae16f1644d873ecad843b0307b143362d4c42172", "_spec": "shebang-regex@^3.0.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\shebang-command", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/shebang-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching a shebang line", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/shebang-regex#readme", "keywords": ["regex", "regexp", "shebang", "match", "test", "line"], "license": "MIT", "name": "shebang-regex", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/shebang-regex.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.0.0"}