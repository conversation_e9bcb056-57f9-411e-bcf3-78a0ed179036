{"_from": "string.prototype.trimend@^1.0.9", "_id": "string.prototype.trimend@1.0.9", "_inBundle": false, "_integrity": "sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==", "_location": "/string.prototype.trimend", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "string.prototype.trimend@^1.0.9", "name": "string.prototype.trimend", "escapedName": "string.prototype.trimend", "rawSpec": "^1.0.9", "saveSpec": null, "fetchSpec": "^1.0.9"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "_shasum": "62e2731272cd285041b36596054e9f66569b6942", "_spec": "string.prototype.trimend@^1.0.9", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/String.prototype.trimEnd/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "k<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "deprecated": false, "description": "ES2019 spec-compliant String.prototype.trimEnd shim.", "devDependencies": {"@es-shims/api": "^2.5.1", "@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "functions-have-names": "^1.2.3", "has-strict-mode": "^1.0.1", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/es-shims/String.prototype.trimEnd#readme", "keywords": ["es6", "es7", "es8", "javascript", "prototype", "polyfill", "utility", "trim", "trimLeft", "trimRight", "trimStart", "trimEnd", "tc39"], "license": "MIT", "main": "index.js", "name": "string.prototype.trimend", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git://github.com/es-shims/String.prototype.trimEnd.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "es-shim-api --bound", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.0.9"}