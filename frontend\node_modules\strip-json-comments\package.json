{"_from": "strip-json-comments@^3.1.1", "_id": "strip-json-comments@3.1.1", "_inBundle": false, "_integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "_location": "/strip-json-comments", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-json-comments@^3.1.1", "name": "strip-json-comments", "escapedName": "strip-json-comments", "rawSpec": "^3.1.1", "saveSpec": null, "fetchSpec": "^3.1.1"}, "_requiredBy": ["/@eslint/eslintrc"], "_resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "_shasum": "31f1281b3832630434831c310c01cccda8cbe006", "_spec": "strip-json-comments@^3.1.1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\@eslint\\eslintrc", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/strip-json-comments/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Strip comments from JSON. Lets you use comments in your JSON files!", "devDependencies": {"ava": "^1.4.1", "matcha": "^0.7.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/strip-json-comments#readme", "keywords": ["json", "strip", "comments", "remove", "delete", "trim", "multiline", "parse", "config", "configuration", "settings", "util", "env", "environment", "jsonc"], "license": "MIT", "name": "strip-json-comments", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/strip-json-comments.git"}, "scripts": {"bench": "matcha benchmark.js", "test": "xo && ava && tsd"}, "version": "3.1.1"}