{"_from": "text-table@^0.2.0", "_id": "text-table@0.2.0", "_inBundle": false, "_integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==", "_location": "/text-table", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "text-table@^0.2.0", "name": "text-table", "escapedName": "text-table", "rawSpec": "^0.2.0", "saveSpec": null, "fetchSpec": "^0.2.0"}, "_requiredBy": ["/eslint"], "_resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "_shasum": "7f5ee823ae805207c00af2df4a84ec3fcfa570b4", "_spec": "text-table@^0.2.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\eslint", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "bugs": {"url": "https://github.com/substack/text-table/issues"}, "bundleDependencies": false, "deprecated": false, "description": "borderless text tables with alignment", "devDependencies": {"cli-color": "~0.2.3", "tap": "~0.4.0", "tape": "~1.0.2"}, "homepage": "https://github.com/substack/text-table", "keywords": ["text", "table", "align", "ascii", "rows", "tabular"], "license": "MIT", "main": "index.js", "name": "text-table", "repository": {"type": "git", "url": "git://github.com/substack/text-table.git"}, "scripts": {"test": "tap test/*.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "chrome/20..latest", "firefox/10..latest", "safari/latest", "opera/11.0..latest", "iphone/6", "ipad/6"]}, "version": "0.2.0"}