{"_from": "type-check@~0.4.0", "_id": "type-check@0.4.0", "_inBundle": false, "_integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "_location": "/type-check", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "type-check@~0.4.0", "name": "type-check", "escapedName": "type-check", "rawSpec": "~0.4.0", "saveSpec": null, "fetchSpec": "~0.4.0"}, "_requiredBy": ["/levn", "/optionator"], "_resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "_shasum": "07b8203bfa7056c0657050e3ccd2c37730bab8f1", "_spec": "type-check@~0.4.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\levn", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/gkz/type-check/issues"}, "bundleDependencies": false, "dependencies": {"prelude-ls": "^1.2.1"}, "deprecated": false, "description": "type-check allows you to check the types of JavaScript values at runtime with a Haskell like type syntax.", "devDependencies": {"browserify": "^16.5.1", "livescript": "^1.6.0", "mocha": "^7.1.1"}, "engines": {"node": ">= 0.8.0"}, "files": ["lib", "README.md", "LICENSE"], "homepage": "https://github.com/gkz/type-check", "keywords": ["type", "check", "checking", "library"], "license": "MIT", "main": "./lib/", "name": "type-check", "repository": {"type": "git", "url": "git://github.com/gkz/type-check.git"}, "scripts": {"test": "make test"}, "version": "0.4.0"}