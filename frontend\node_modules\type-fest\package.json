{"_from": "type-fest@^0.20.2", "_id": "type-fest@0.20.2", "_inBundle": false, "_integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "_location": "/type-fest", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "type-fest@^0.20.2", "name": "type-fest", "escapedName": "type-fest", "rawSpec": "^0.20.2", "saveSpec": null, "fetchSpec": "^0.20.2"}, "_requiredBy": ["/globals"], "_resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "_shasum": "1bf207f4b28f91583666cb5fbd327887301cd5f4", "_spec": "type-fest@^0.20.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\globals", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/type-fest/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A collection of essential TypeScript types", "devDependencies": {"@sindresorhus/tsconfig": "~0.7.0", "tsd": "^0.13.1", "typescript": "^4.1.2", "xo": "^0.35.0"}, "engines": {"node": ">=10"}, "files": ["index.d.ts", "base.d.ts", "source", "ts41"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/type-fest#readme", "keywords": ["typescript", "ts", "types", "utility", "util", "utilities", "omit", "merge", "json"], "license": "(MIT OR CC0-1.0)", "name": "type-fest", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/type-fest.git"}, "scripts": {"//test": "xo && tsd && tsc", "test": "xo && tsc"}, "types": "./index.d.ts", "typesVersions": {">=4.1": {"*": ["ts41/*"]}}, "version": "0.20.2", "xo": {"rules": {"@typescript-eslint/ban-types": "off", "@typescript-eslint/indent": "off", "node/no-unsupported-features/es-builtins": "off"}}}