{"_from": "unbox-primitive@^1.1.0", "_id": "unbox-primitive@1.1.0", "_inBundle": false, "_integrity": "sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==", "_location": "/unbox-primitive", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "unbox-primitive@^1.1.0", "name": "unbox-primitive", "escapedName": "unbox-primitive", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/es-abstract"], "_resolved": "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz", "_shasum": "8d9d2c9edeea8460c7f35033a88867944934d1e2", "_spec": "unbox-primitive@^1.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/unbox-primitive/issues"}, "bundleDependencies": false, "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "deprecated": false, "description": "Unbox a boxed JS primitive value.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/object-is": "^1.1.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "object-is": "^1.1.6", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/unbox-primitive#readme", "keywords": ["unbox", "boxed", "primitive", "object", "javascript", "ecmascript"], "license": "MIT", "main": "index.js", "name": "unbox-primitive", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/unbox-primitive.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.1.0"}