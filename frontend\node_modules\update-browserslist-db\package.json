{"_from": "update-browserslist-db@^1.1.3", "_id": "update-browserslist-db@1.1.3", "_inBundle": false, "_integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "_location": "/update-browserslist-db", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "update-browserslist-db@^1.1.3", "name": "update-browserslist-db", "escapedName": "update-browserslist-db", "rawSpec": "^1.1.3", "saveSpec": null, "fetchSpec": "^1.1.3"}, "_requiredBy": ["/browserslist"], "_resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "_shasum": "348377dd245216f9e7060ff50b15a1b740b75420", "_spec": "update-browserslist-db@^1.1.3", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\browserslist", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bin": {"update-browserslist-db": "cli.js"}, "bugs": {"url": "https://github.com/browserslist/update-db/issues"}, "bundleDependencies": false, "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "deprecated": false, "description": "CLI tool to update caniuse-lite to refresh target browsers from Browserslist config", "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "homepage": "https://github.com/browserslist/update-db#readme", "keywords": ["caniuse", "browsers", "target"], "license": "MIT", "name": "update-browserslist-db", "peerDependencies": {"browserslist": ">= 4.21.0"}, "repository": {"type": "git", "url": "git+https://github.com/browserslist/update-db.git"}, "types": "./index.d.ts", "version": "1.1.3"}