{"_from": "which-boxed-primitive@^1.1.0", "_id": "which-boxed-primitive@1.1.1", "_inBundle": false, "_integrity": "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==", "_location": "/which-boxed-primitive", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which-boxed-primitive@^1.1.0", "name": "which-boxed-primitive", "escapedName": "which-boxed-primitive", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/unbox-primitive", "/which-builtin-type"], "_resolved": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "_shasum": "d76ec27df7fa165f18d5808374a5fe23c29b176e", "_spec": "which-boxed-primitive@^1.1.0", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-builtin-type", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/which-boxed-primitive/issues"}, "bundleDependencies": false, "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "deprecated": false, "description": "Which kind of boxed JS primitive is this?", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/which-boxed-primitive#readme", "keywords": ["boxed", "primitive", "object", "ecmascript", "javascript", "which"], "license": "MIT", "main": "index.js", "name": "which-boxed-primitive", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-boxed-primitive.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "version": "1.1.1"}