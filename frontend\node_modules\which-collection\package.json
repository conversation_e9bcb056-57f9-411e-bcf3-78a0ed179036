{"_from": "which-collection@^1.0.2", "_id": "which-collection@1.0.2", "_inBundle": false, "_integrity": "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==", "_location": "/which-collection", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which-collection@^1.0.2", "name": "which-collection", "escapedName": "which-collection", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/which-builtin-type"], "_resolved": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz", "_shasum": "627ef76243920a107e7ce8e96191debe4b16c2a0", "_spec": "which-collection@^1.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\which-builtin-type", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/which-collection/issues"}, "bundleDependencies": false, "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "deprecated": false, "description": "Which kind of Collection (Map, Set, WeakMap, WeakSet) is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.15.0", "@ljharb/eslint-config": "^21.1.0", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.8.4", "@types/tape": "^5.6.4", "aud": "^2.0.4", "auto-changelog": "^2.4.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.1", "safe-publish-latest": "^2.0.0", "tape": "^5.7.5", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/which-collection#readme", "keywords": ["map", "set", "weakmap", "weakset", "collection.es6", "es2015"], "license": "MIT", "main": "index.js", "name": "which-collection", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/which-collection.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx aud --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "version": "1.0.2"}