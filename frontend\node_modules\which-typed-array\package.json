{"_from": "which-typed-array@^1.1.19", "_id": "which-typed-array@1.1.19", "_inBundle": false, "_integrity": "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==", "_location": "/which-typed-array", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which-typed-array@^1.1.19", "name": "which-typed-array", "escapedName": "which-typed-array", "rawSpec": "^1.1.19", "saveSpec": null, "fetchSpec": "^1.1.19"}, "_requiredBy": ["/es-abstract", "/is-typed-array", "/which-builtin-type"], "_resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz", "_shasum": "df03842e870b6b88e117524a4b364b6fc689f956", "_spec": "which-typed-array@^1.1.19", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\es-abstract", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/inspect-js/which-typed-array/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://ljharb.codes"}], "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "deprecated": false, "description": "Which kind of Typed Array is this JavaScript value? Works cross-realm, without `instanceof`, and despite Symbol.toStringTag.", "devDependencies": {"@arethetypeswrong/cli": "^0.17.4", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.3.2", "@types/call-bind": "^1.0.5", "@types/for-each": "^0.3.3", "@types/gopd": "^1.0.3", "@types/is-callable": "^1.1.2", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "in-publish": "^2.0.1", "is-callable": "^1.2.7", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/inspect-js/which-typed-array#readme", "keywords": ["array", "TypedArray", "typed array", "which", "typed", "Int8Array", "Uint8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "ES6", "toStringTag", "Symbol.toStringTag", "@@toStringTag"], "license": "MIT", "main": "index.js", "name": "which-typed-array", "publishConfig": {"ignore": [".github/workflows", "types.d.ts"]}, "repository": {"type": "git", "url": "git://github.com/inspect-js/which-typed-array.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run --silent lint", "test": "npm run tests-only && npm run test:harmony", "test:harmony": "nyc node --harmony --es-staging test", "tests-only": "nyc tape test", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js", "browsers": ["iexplore/6.0..latest", "firefox/3.0..6.0", "firefox/15.0..latest", "firefox/nightly", "chrome/4.0..10.0", "chrome/20.0..latest", "chrome/canary", "opera/10.0..latest", "opera/next", "safari/4.0..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2"]}, "types": "./index.d.ts", "version": "1.1.19"}