{"_from": "wrappy@1", "_id": "wrappy@1.0.2", "_inBundle": false, "_integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "_location": "/wrappy", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wrappy@1", "name": "wrappy", "escapedName": "wrappy", "rawSpec": "1", "saveSpec": null, "fetchSpec": "1"}, "_requiredBy": ["/inflight", "/once"], "_resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "_shasum": "b5243d8f3ec1aa35f1364605bc0d1036e30ab69f", "_spec": "wrappy@1", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\inflight", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/npm/wrappy/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Callback wrapping utility", "devDependencies": {"tap": "^2.3.1"}, "directories": {"test": "test"}, "files": ["wrappy.js"], "homepage": "https://github.com/npm/wrappy", "license": "ISC", "main": "wrappy.js", "name": "wrappy", "repository": {"type": "git", "url": "git+https://github.com/npm/wrappy.git"}, "scripts": {"test": "tap --coverage test/*.js"}, "version": "1.0.2"}