{"_from": "yallist@^3.0.2", "_id": "yallist@3.1.1", "_inBundle": false, "_integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "_location": "/yallist", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yallist@^3.0.2", "name": "yallist", "escapedName": "yallist", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/lru-cache"], "_resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "_shasum": "dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd", "_spec": "yallist@^3.0.2", "_where": "D:\\Desktop\\AI\\frontend\\node_modules\\lru-cache", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Yet Another Linked List", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "homepage": "https://github.com/isaacs/yallist#readme", "license": "ISC", "main": "yallist.js", "name": "yallist", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "3.1.1"}