import React, { useState, useEffect } from 'react';
import ImageGenerator from './components/ImageGenerator';
import TextGenerator from './components/TextGenerator';
import { ApiService } from './services/api';

function App() {
  const [activeTab, setActiveTab] = useState('text');
  const [connectionStatus, setConnectionStatus] = useState('checking');

  // 检查后端连接状态
  useEffect(() => {
    const checkConnection = async () => {
      try {
        await ApiService.healthCheck();
        setConnectionStatus('connected');
      } catch (error) {
        setConnectionStatus('disconnected');
        console.error('后端连接失败:', error.message);
      }
    };

    checkConnection();
    // 每30秒检查一次连接状态
    const interval = setInterval(checkConnection, 30000);
    return () => clearInterval(interval);
  }, []);

  const getConnectionStatusDisplay = () => {
    switch (connectionStatus) {
      case 'checking':
        return { text: '检查中...', color: '#6c757d' };
      case 'connected':
        return { text: '已连接', color: '#28a745' };
      case 'disconnected':
        return { text: '连接失败', color: '#dc3545' };
      default:
        return { text: '未知', color: '#6c757d' };
    }
  };

  const statusDisplay = getConnectionStatusDisplay();

  return (
    <div className="container">
      {/* 头部 */}
      <header style={{ textAlign: 'center', marginBottom: '32px' }}>
        <h1 style={{ 
          fontSize: '32px', 
          fontWeight: '700', 
          color: '#2c3e50', 
          marginBottom: '8px' 
        }}>
          AI内容生成器
        </h1>
        <p style={{ 
          fontSize: '16px', 
          color: '#6c757d', 
          marginBottom: '16px' 
        }}>
          智能文案创作 & 图片生成平台
        </p>
        
        {/* 连接状态指示器 */}
        <div style={{ 
          display: 'inline-flex', 
          alignItems: 'center', 
          gap: '8px',
          padding: '6px 12px',
          background: '#f8f9fa',
          borderRadius: '20px',
          fontSize: '14px'
        }}>
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: statusDisplay.color
          }}></div>
          <span style={{ color: statusDisplay.color }}>
            后端服务: {statusDisplay.text}
          </span>
        </div>
      </header>

      {/* 功能切换标签 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        marginBottom: '32px',
        borderBottom: '1px solid #e9ecef'
      }}>
        <button
          className={`btn ${activeTab === 'text' ? 'btn-primary' : 'btn-secondary'}`}
          onClick={() => setActiveTab('text')}
          style={{
            borderRadius: '6px 6px 0 0',
            border: 'none',
            borderBottom: activeTab === 'text' ? '2px solid #6c757d' : '2px solid transparent'
          }}
        >
          文案生成
        </button>
        <button
          className={`btn ${activeTab === 'image' ? 'btn-primary' : 'btn-secondary'}`}
          onClick={() => setActiveTab('image')}
          style={{
            borderRadius: '6px 6px 0 0',
            border: 'none',
            borderBottom: activeTab === 'image' ? '2px solid #6c757d' : '2px solid transparent',
            marginLeft: '8px'
          }}
        >
          图片生成
        </button>
      </div>

      {/* 连接失败提示 */}
      {connectionStatus === 'disconnected' && (
        <div className="error" style={{ marginBottom: '24px' }}>
          <strong>后端服务连接失败</strong>
          <br />
          请确保后端服务已启动并运行在 http://localhost:5000
          <br />
          启动命令: <code>cd backend && python app.py</code>
        </div>
      )}

      {/* 主要内容区域 */}
      <main>
        {activeTab === 'text' && <TextGenerator />}
        {activeTab === 'image' && <ImageGenerator />}
      </main>

      {/* 页脚 */}
      <footer style={{ 
        textAlign: 'center', 
        marginTop: '48px', 
        padding: '24px 0',
        borderTop: '1px solid #e9ecef',
        color: '#6c757d',
        fontSize: '14px'
      }}>
        <p>AI内容生成器 - 基于豆包API构建</p>
        <p style={{ marginTop: '8px' }}>
          支持文案创作和图片生成，让创意无限可能
        </p>
      </footer>
    </div>
  );
}

export default App;
