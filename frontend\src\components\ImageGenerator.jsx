import React, { useState, useEffect } from 'react';
import { ApiService } from '../services/api';

const ImageGenerator = () => {
  const [prompt, setPrompt] = useState('');
  const [options, setOptions] = useState({
    size: '2K',
    sequential_image_generation: 'disabled',
    stream: false,
    response_format: 'url',
    watermark: true
  });
  const [availableOptions, setAvailableOptions] = useState(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  // 加载可用选项
  useEffect(() => {
    const loadOptions = async () => {
      try {
        const response = await ApiService.getOptions();
        if (response.success) {
          setAvailableOptions(response.data.image);
        }
      } catch (err) {
        console.error('加载选项失败:', err);
      }
    };
    loadOptions();
  }, []);

  // 生成图片
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('请输入图片描述');
      return;
    }

    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await ApiService.generateImage({
        prompt: prompt.trim(),
        ...options
      });

      if (response.success) {
        setResult(response.data);
      } else {
        setError(response.error || '图片生成失败');
      }
    } catch (err) {
      setError(err.message || '图片生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新选项
  const updateOption = (key, value) => {
    setOptions(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="card">
      <div className="card-header">
        <h2 className="card-title">AI图片生成</h2>
      </div>

      {error && <div className="error">{error}</div>}

      <div className="form-group">
        <label className="form-label">图片描述 *</label>
        <textarea
          className="form-textarea"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="请详细描述您想要生成的图片内容..."
          rows={4}
        />
      </div>

      {availableOptions && (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px', marginBottom: '20px' }}>
          {/* 图片尺寸 */}
          <div className="form-group">
            <label className="form-label">图片尺寸</label>
            <select
              className="form-select"
              value={options.size}
              onChange={(e) => updateOption('size', e.target.value)}
            >
              {availableOptions.sizes.map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
          </div>

          {/* 响应格式 */}
          <div className="form-group">
            <label className="form-label">响应格式</label>
            <select
              className="form-select"
              value={options.response_format}
              onChange={(e) => updateOption('response_format', e.target.value)}
            >
              {availableOptions.formats.map(format => (
                <option key={format} value={format}>
                  {format === 'url' ? 'URL链接' : 'Base64编码'}
                </option>
              ))}
            </select>
          </div>

          {/* 序列生成 */}
          <div className="form-group">
            <label className="form-label">序列生成</label>
            <select
              className="form-select"
              value={options.sequential_image_generation}
              onChange={(e) => updateOption('sequential_image_generation', e.target.value)}
            >
              <option value="disabled">禁用</option>
              <option value="enabled">启用</option>
            </select>
          </div>

          {/* 水印 */}
          <div className="form-group">
            <label className="form-label">水印</label>
            <select
              className="form-select"
              value={options.watermark}
              onChange={(e) => updateOption('watermark', e.target.value === 'true')}
            >
              <option value="true">启用</option>
              <option value="false">禁用</option>
            </select>
          </div>

          {/* 流式生成 */}
          <div className="form-group">
            <label className="form-label">流式生成</label>
            <select
              className="form-select"
              value={options.stream}
              onChange={(e) => updateOption('stream', e.target.value === 'true')}
            >
              <option value="false">禁用</option>
              <option value="true">启用</option>
            </select>
          </div>
        </div>
      )}

      <button
        className="btn btn-primary"
        onClick={handleGenerate}
        disabled={loading || !prompt.trim()}
      >
        {loading ? (
          <>
            <div className="spinner"></div>
            生成中...
          </>
        ) : (
          '生成图片'
        )}
      </button>

      {result && (
        <div className="image-result">
          <h3 style={{ marginBottom: '16px', color: '#495057' }}>生成结果</h3>
          {result.data && result.data.length > 0 && (
            <div>
              <img
                src={result.data[0].url}
                alt="生成的图片"
                className="generated-image"
                onError={(e) => {
                  e.target.style.display = 'none';
                  setError('图片加载失败');
                }}
              />
              <p style={{ marginTop: '12px', color: '#6c757d', fontSize: '14px' }}>
                尺寸: {result.data[0].size}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ImageGenerator;
