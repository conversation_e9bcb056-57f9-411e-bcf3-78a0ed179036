import React, { useState, useEffect } from 'react';
import { ApiService } from '../services/api';

const TextGenerator = () => {
  const [theme, setTheme] = useState('');
  const [style, setStyle] = useState('创意');
  const [length, setLength] = useState('中等');
  const [availableOptions, setAvailableOptions] = useState(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');

  // 加载可用选项
  useEffect(() => {
    const loadOptions = async () => {
      try {
        const response = await ApiService.getOptions();
        if (response.success) {
          setAvailableOptions(response.data.text);
        }
      } catch (err) {
        console.error('加载选项失败:', err);
      }
    };
    loadOptions();
  }, []);

  // 生成文案
  const handleGenerate = async () => {
    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await ApiService.generateText({
        theme: theme.trim(),
        style,
        length
      });

      if (response.success) {
        setResult(response.data);
      } else {
        setError(response.error || '文案生成失败');
      }
    } catch (err) {
      setError(err.message || '文案生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 复制文案到剪贴板
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // 简单的成功提示
      const originalText = document.querySelector('.copy-btn').textContent;
      document.querySelector('.copy-btn').textContent = '已复制!';
      setTimeout(() => {
        if (document.querySelector('.copy-btn')) {
          document.querySelector('.copy-btn').textContent = originalText;
        }
      }, 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h2 className="card-title">AI文案生成</h2>
      </div>

      {error && <div className="error">{error}</div>}

      <div className="form-group">
        <label className="form-label">主题关键词</label>
        <input
          type="text"
          className="form-input"
          value={theme}
          onChange={(e) => setTheme(e.target.value)}
          placeholder="输入主题关键词（可选）"
        />
      </div>

      {availableOptions && (
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px', marginBottom: '20px' }}>
          {/* 文案风格 */}
          <div className="form-group">
            <label className="form-label">文案风格</label>
            <select
              className="form-select"
              value={style}
              onChange={(e) => setStyle(e.target.value)}
            >
              {availableOptions.styles.map(styleOption => (
                <option key={styleOption} value={styleOption}>{styleOption}</option>
              ))}
            </select>
          </div>

          {/* 文案长度 */}
          <div className="form-group">
            <label className="form-label">文案长度</label>
            <select
              className="form-select"
              value={length}
              onChange={(e) => setLength(e.target.value)}
            >
              {availableOptions.lengths.map(lengthOption => (
                <option key={lengthOption} value={lengthOption}>{lengthOption}</option>
              ))}
            </select>
          </div>
        </div>
      )}

      <button
        className="btn btn-primary"
        onClick={handleGenerate}
        disabled={loading}
      >
        {loading ? (
          <>
            <div className="spinner"></div>
            生成中...
          </>
        ) : (
          '生成文案'
        )}
      </button>

      {result && (
        <div style={{ marginTop: '24px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <h3 style={{ color: '#495057', margin: 0 }}>生成结果</h3>
            <button
              className="btn btn-secondary copy-btn"
              onClick={() => copyToClipboard(result.text)}
            >
              复制文案
            </button>
          </div>
          
          <div style={{ 
            background: '#f8f9fa', 
            padding: '20px', 
            borderRadius: '8px', 
            border: '1px solid #e9ecef',
            lineHeight: '1.8'
          }}>
            <p style={{ margin: 0, fontSize: '16px', color: '#2c3e50' }}>
              {result.text}
            </p>
          </div>

          <div style={{ 
            marginTop: '16px', 
            padding: '12px', 
            background: '#e9ecef', 
            borderRadius: '6px',
            fontSize: '14px',
            color: '#6c757d'
          }}>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '8px' }}>
              <div><strong>主题:</strong> {result.theme || '随机'}</div>
              <div><strong>风格:</strong> {result.style}</div>
              <div><strong>长度:</strong> {result.length}</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TextGenerator;
