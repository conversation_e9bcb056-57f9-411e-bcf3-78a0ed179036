@echo off
echo ========================================
echo AI内容生成器启动脚本
echo ========================================

echo.
echo 1. 启动后端服务...
cd backend
start "AI后端服务" cmd /k "python app.py"
cd ..

echo.
echo 2. 等待后端服务启动...
timeout /t 3 /nobreak > nul

echo.
echo 3. 启动前端服务...
cd frontend
start "AI前端服务" cmd /k "python -m http.server 3000"
cd ..

echo.
echo 4. 等待前端服务启动...
timeout /t 2 /nobreak > nul

echo.
echo 5. 打开浏览器...
start http://localhost:3000

echo.
echo ========================================
echo 服务启动完成！
echo 前端地址: http://localhost:3000
echo 后端地址: http://localhost:5000
echo ========================================
echo.
echo 按任意键退出...
pause > nul
